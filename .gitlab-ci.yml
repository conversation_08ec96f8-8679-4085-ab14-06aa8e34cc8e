stages:
  - prepare
  - test-scale
  - test
  - finish

variables:
  TEST_ENVIRONMENT:
    value: "none"
    description: "Selecione o ambiente"
    options:
      - "staging"
      - "prod"
      - "oci-prod"
      - "oci-prod-t3"
      - "none"
  CYPRESS_COMMIT_MESSAGE:
    value: ""
    description: "Mensagem que será exibida no run do cypress dashboard ou cypress sorry"
  CYPRESS_SPEC:
    value: ""
    description: "Especifique o caminho do teste. Ex.: cypress/integration/2-Cadastros/**/*"
  CYPRESS_REPLICAS:
    value: "35"
    description: "Número de testes simultâneos"
  CYPRESS_SORRY:
    value: "true"
    description: "Define se o teste deve ser executado com coordenador http://cypress.pactoteste.com"
    options:
      - "true"
      - "false"
  CLEANUP:
    value: "true"
    description: "Limpar ambiente de teste"
    options:
      - "true"
      - "false"
  CLEANUP_PROXY:
    value: "false"
    description: "Refazer o proxy"
    options:
      - "true"
      - "false"
  APP_BRANCHS:
    value: "zw=master,treino-front=master"
    description: >
      Esta é a descrição da variável de ambiente APP_BRANCHS.
      Ela é usada para definir as branches das aplicações.
      Por exemplo: zw=feature/123,treino-front=hotfix/123.
      Os nomes das aplicações (como "zw" e "treino-front") devem corresponder aos nomes definidos nos serviços dos templates do Docker Compose.
  TEST_RETRY:
    value: "true"
    description: "Executar 1 vez todos os testes que falharam"
    options:
      - "true"
      - "false"
  CYPRESS_BURN:
    value: ""
    description: "Executar n vezes cada teste que executado na pipeline"

include:
  - local: .gitlab-ci-prepare.yml
  - local: .gitlab-ci-test.yml
  - local: .gitlab-ci-finish.yml
  - local: .gitlab-ci-stages.yml
