## Comandos Personalizados do Cypress

Este documento descreve todos os comandos personalizados cadastrados no Cypress para auxiliar a equipe de QA durante os testes. Os comandos estão organizados com suas descrições, parâmetros e exemplos de uso.

---

### 1. `loginComApi`
- **Descrição:** Realiza login com API para um módulo específico, utilizando chave, usuário e senha configurados.
- **Parâmetros:**
  - `empresa` (opcional): Identificação da empresa.
  - `chave` (opcional): Chave de autenticação.
  - `usuario` (opcional): Usuário para login.
  - `senha` (opcional): Senha do usuário.
  - `modulo` (string): <PERSON><PERSON><PERSON><PERSON> para login, como `adm`, `fin`, ou `crm`.
  - `modulo` : <PERSON><PERSON><PERSON><PERSON> para login, como adm`loginComApi`, fin`loginFinCom<PERSON>pi`, crm`loginCrmComApi`, treino`loginTreinoComApi`, agenda`loginAgendaComApi`.
  - `default` : se for acessar o sistema com o usuário PactoBR não é necessário preencher os campos do (), pode deixar ele vazio.  
- **Exemplo de Uso:**
  ```javascript
  cy.loginComApi(1, "minhaChave", "usuario123", "senhaSegura", null, "adm");
  ```

---

### 2. `novoLogin`
- **Descrição:** Realiza login no novo fluxo do sistema, selecionando unidade e módulo.
- **Parâmetros:**
  - `modulo` (string): Define o módulo (`adm`, `admBeta`, `treino`, etc.).
  - `usuario` (string): Nome de usuário.
  - `senha` (string): Senha do usuário.
- **Exemplo de Uso:**
  ```javascript
  cy.novoLogin("adm", "usuario123", "senhaSegura");
  ```

---

### 3. `fixture` (Sobrescrito)
- **Descrição:** Sobrescreve o comando padrão `cy.fixture`. Caso o nome da fixture seja `"login"`, retorna o usuário configurado em `Cypress.env("USUARIO_PACTOBR")`. Para outros nomes de fixtures, utiliza o comportamento padrão.
- **Exemplo de Uso:**
  ```javascript
  cy.fixture("login").then((user) => {
    expect(user).to.have.property("username");
  });
  ```

---

### 4. `menuExplorar`
- **Descrição:** Navega pelo menu explorar até uma funcionalidade específica.
- **Parâmetros:**
  - `nomeFuncionalidade` (string): Nome da funcionalidade no menu.
  - `siglaModulo` (string, opcional): Sigla do módulo ao qual a funcionalidade pertence.
- **Exemplo de Uso:**
  ```javascript
  cy.menuExplorar("Dashboard", "ADM");
  ```

---

### 5. `FluxoCaixa`
- **Descrição:** Automatiza ações relacionadas ao fluxo de caixa.
- **Exemplo de Uso:**
  ```javascript
  cy.FluxoCaixa();
  ```

---

### 6. `scroolTop`
- **Descrição:** Realiza scroll até o topo da página.
- **Exemplo de Uso:**
  ```javascript
  cy.scroolTop();
  ```

---

### 7. `InputDate`
- **Descrição:** Interage com campos de entrada de data no sistema.
- **Formato de Data Esperado:** Deve ser passado no formato `YYYY-MM-DD`.
- **Comportamento em Casos de Erro:** Se o formato de data for inválido ou o campo não puder ser encontrado, o comando lançará um erro no console.
- **Exemplo de Uso:**
  ```javascript
  cy.InputDate("#data-inicial", "2025-01-13");
  ```

---

### 8. `angularSwitch`
- **Descrição:** Alterna o checkbox da nova versão do Angular no sistema.
- **Exemplo de Uso:**
  ```javascript
  cy.angularSwitch();
  ```

---

### 9. `token`
- **Descrição:** Gera um token de autenticação utilizando a API de login.
- **Parâmetros:**
  - `chave` (string): Chave de autenticação.
  - `usuario` (string): Nome do usuário.
  - `senha` (string): Senha do usuário.
- **Exemplo de Uso:**
  ```javascript
  cy.token("chave123", "usuario123", "senhaSegura");
  ```

---

### 10. `linkZw`
- **Descrição:** Gera um link para o módulo ZW utilizando token de autenticação.
- **Parâmetros:**
  - `token` (string): Token gerado previamente.
  - `empresa` (string): Identificação da empresa.
- **Exemplo de Uso:**
  ```javascript
  cy.linkZw("token123", "empresa1");
  ```

---

### 11. `logout`
- **Descrição:** Realiza logout do sistema, limpando cookies e redirecionando para a tela de login.
- **Exemplo de Uso:**
  ```javascript
  cy.logout();
  ```

---

### 12. `waitVisible`
- **Descrição:** Aguarda até que um elemento específico esteja visível na página.
- **Parâmetros:**
  - `selector` (string): Seletor CSS do elemento.
  - `time` (opcional): Tempo máximo de espera em milissegundos.
- **Exemplo de Uso:**
  ```javascript
  cy.waitVisible("#meuElemento", 5000);
  ```

---

### 13. `value`
- **Descrição:** Define um valor para um campo de entrada.
- **Parâmetros:**
  - `selector` (string): Seletor CSS do campo.
  - `value` (string): Valor a ser definido.
- **Exemplo de Uso:**
  ```javascript
  cy.value("#meuInput", "novoValor");
  ```

---

### 14. `CadastrarClienteComBV`
- **Descrição:** Cadastra um cliente com BV (Benefício Vinculado).
- **Exemplo de Uso:**
  ```javascript
  cy.CadastrarClienteComBV();
  ```

---

### Contribuindo
Caso precise adicionar ou atualizar comandos, edite o arquivo de comandos do Cypress e atualize este documento com as novas informações.

---

### Referências
- [Documentação Oficial do Cypress](https://docs.cypress.io/)
- [Boas Práticas para Comandos Personalizados](https://docs.cypress.io/guides/references/best-practices)
