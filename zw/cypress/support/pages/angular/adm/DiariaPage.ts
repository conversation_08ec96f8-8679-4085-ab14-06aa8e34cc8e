import { DiariaPage } from '../../../locators';
class DiariaPageClass {
  elements = {
    SELECT_CLIENTE: '#select-codigo-cliente',
    INPUT_CLIENTE: '#select-codigo-cliente .ds3-select-input',
    OPTION: '.ds3-option',
    SELECT_OPTIONS: '.ds3-select-options',
    SELECT_PRODUTO: ':nth-child(2) > .ds3-form-field > .content > .input-area > .infix > #select-codigo-produto > .ds3-select',
    SELECT_MODALIDADE: ':nth-child(3) > .ds3-form-field > .content > .input-area > .infix > #select-codigo-produto > .ds3-select',
    BOTAO_RECEBER: '#btn-receber-receber',
    INPUT_SENHA: '#aut-input-psw',
    CONFIRMA_SENHA: '#aut-btn-confirm'
  }

  lancaDiaria(nomeCliente: string, usaSenha: boolean = true): void {
    cy.wait(2000)
    cy.get(DiariaPage.SELECT_CLIENTE).click()
    cy.get(DiariaPage.INPUT_CLIENTE).type(nomeCliente)
    cy.get(DiariaPage.OPTION).contains(nomeCliente).click()
    cy.get(DiariaPage.SELECT_PRODUTO).click()
    cy.get(DiariaPage.SELECT_OPTIONS).contains('TESTE').click()
    cy.get(DiariaPage.SELECT_MODALIDADE).click()
    cy.get(DiariaPage.SELECT_OPTIONS).contains('FAZ TUDO').click()
    cy.get(DiariaPage.BOTAO_RECEBER).click()
    if(usaSenha) {
      cy.get(DiariaPage.INPUT_SENHA).type('123')
      cy.get(DiariaPage.CONFIRMA_SENHA).click()
    }
    cy.contains('Diária lançada com sucesso!')
  }

}

export default new DiariaPageClass()