import { BancoPage   } from '../../../locators';

class BancoPageClass {

    adicionarBanco() {
        cy.get(BancoPage.ADICIONAR).click()
        cy.get(BancoPage.NOME).click().type('Nubank');
        cy.get(BancoPage.CODIGO).eq(1).click().type('386');
        cy.contains('Salvar').click()
        cy.contains('Banco cadastrado com sucesso!').should('be.visible')

    }

    editarBanco() {
        cy.get(BancoPage.BUSCA, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('Nubank');

        cy.contains('td', '386') 
            .parent('tr') 
            .within(() => {
                cy.get(BancoPage.EDITAR).click(); 
            });

        cy.get(BancoPage.NOME, { timeout: 15000 }).clear().type('Nubank TESTE');
        cy.contains('Salvar').click()
        cy.contains('Banco cadastrado com sucesso!').should('be.visible')

    }

    excluirBanco() {
        cy.get(BancoPage.BUSCA, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('386');

        cy.contains('td', '386') 
            .parent('tr') 
            .within(() => {
                cy.get(BancoPage.EXCLUIR).click(); 
            });

            cy.contains('Cadastro de banco excluído com sucesso').should('be.visible')

    }
}


export default new BancoPageClass();