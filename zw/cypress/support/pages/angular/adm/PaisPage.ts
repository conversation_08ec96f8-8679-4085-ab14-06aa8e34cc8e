import { PaisPage  } from '../../../locators';
const { faker } = require('@faker-js/faker');

class PaisPageClass {

    adicionarPais() {
        const nomePais = faker.address.country();
        const nomeNacionalidade = faker.address.country();

        cy.get(PaisPage.ADICIONAR).click();
        cy.get(PaisPage.NOME).type(nomePais);
        cy.get(PaisPage.NACIONALIDADE).type(nomeNacionalidade);
        cy.get(PaisPage.SIGLA).type('GO');
        cy.get(PaisPage.ESTADO).type('Goiás');
        cy.get(PaisPage.ADICIONAR_ESTADO).click();
        cy.contains('Salvar').should('be.visible').click();
        cy.contains('País salvo com sucesso!').should('be.visible');

        return cy.wrap(nomePais);
    }

    editarPais(nomePais) {

        cy.intercept('GET', '**/paises?*').as('buscarPais');
        cy.get(PaisPage.BUSCA).type(nomePais);
        cy.wait('@buscarPais');
        cy.get(PaisPage.LISTA)
            .should('be.visible')
            .click();

        const novoNomePais = faker.address.country();
        cy.get(PaisPage.NOME).clear().type(novoNomePais);

        cy.contains('Salvar').should('be.visible').click();
        cy.contains('País salvo com sucesso!').should('be.visible');

        return cy.wrap(novoNomePais);

    }

    excluirPais(nomePais){
        cy.intercept('GET', '**/paises?*').as('buscarPais');
        cy.get(PaisPage.BUSCA).clear().type(nomePais);
        cy.wait('@buscarPais');

        cy.get(PaisPage.LISTA)
            .should('be.visible')

        cy.get(PaisPage.EXCLUIR, { timeout: 15000 }).click();
        cy.contains('País excluído com sucesso').should('be.visible');

    }
}

export default new PaisPageClass();