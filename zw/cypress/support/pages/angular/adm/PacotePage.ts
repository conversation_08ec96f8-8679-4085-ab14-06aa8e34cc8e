import EnvUtils from '@utils/EnvUtils'
import * as faker from 'faker-br'
import { PacotePage } from '../../../locators';

class PacotePageClass {

    elements = {
        ADICIONAR: '#btn-add-item',
        DESCRICAO: 'input[title="Descrição"]',
        QUANTIDADE_MODALIDADES: 'pacto-cat-select-filter',
        VALOR_PACOTE: 'input[placeholder="199,90"]',
        PACOTE_ADICIONAL: '#input-check-0-input',
        PACOTE_PADRAO: '#input-check-1-input',
        MODALIDADES_ESPECIFICAS: '.checkboxModalidades > .cat-input-wrapper > label > .text',
        SELECT_MODALIDADE: 'pacto-cat-select-filter',
        INPUT_SELECT_MODALIDADE: 'input[placeholder="Filtro..."]',
        VEZES_POR_SEMANA: 'pacto-cat-form-input-number',
        ADICIONAR_MODALIDADE: '#add-modalidade-pacote',
        DELETAR_MODALIDADE: '#element-0-deletepacotemod',
        BUSCA_RAPIDA: '.search > :nth-child(1) > .ds3-form-field > .content > .input-area',
        LISTA_PACOTES: '.table-content',
        EDITAR: '[id$="editpacote"]',
        EXCLUIR: '[id$="deletepacote"]'
    }

    selecionaModalidade(modalidade: string): void {
        cy.get(PacotePage.SELECT_MODALIDADE).click()
        cy.get('.options').contains(modalidade).should('exist').click({force: true})
        cy.get(PacotePage.ADICIONAR_MODALIDADE).click()
    }

    criaPacoteVariavel(valorTotalPacote: string): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos/pacotes')
        const nomePacote: string = `${faker.name.firstName()} ${faker.random.number()}`
        cy.get(PacotePage.ADICIONAR).click()
        cy.get(PacotePage.DESCRICAO).type(nomePacote)
        cy.get(PacotePage.MODALIDADES_ESPECIFICAS).click()
        cy.contains(`${PacotePage.VEZES_POR_SEMANA} span`, 'Quantidade de modalidades').parents('pacto-cat-form-input-number').find('input').type('2')
        cy.get(PacotePage.VALOR_PACOTE).type(valorTotalPacote)
        this.selecionaModalidade('FAZ TUDO')
        this.selecionaModalidade('PLANO NORMAL')
        cy.contains('span', 'Salvar').click()
        cy.contains('Pacote cadastrado com sucesso!').should('be.visible', {timeout: 10000})
        return nomePacote
    }

    editaPacote(nomePacote: string): void {
        cy.get(PacotePage.BUSCA_RAPIDA).type(nomePacote).wait(1000)
        cy.get(PacotePage.LISTA_PACOTES).contains(nomePacote, {timeout: 10000}).click()
        cy.get(PacotePage.DESCRICAO).type(' Editado')
        this.selecionaModalidade('IMPORTACAO')
        cy.contains('span', 'Salvar').click()
        cy.contains('Pacote cadastrado com sucesso!').should('be.visible', {timeout: 10000})
    }

    excluiPacote(nomePacote: string): void {
        cy.get(PacotePage.LISTA_PACOTES).contains(nomePacote, {timeout: 10000}).should('be.visible')
        cy.get(PacotePage.EXCLUIR).click()
        cy.contains('span', 'Excluir').click({force: true}).wait(1000)
        cy.contains(`Tem certeza que deseja excluir o pacote`)
        cy.contains('span', 'Excluir').click()
        cy.contains(`Nenhum pacote encontrado`).should('be.visible')
    }

    criaPacoteFixo(valorModalidade1: string, valorModalidade2: string, valorModalidade3: string): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos/pacotes')
        const nomePacote: string = `PACOTE FIXO ${faker.random.number()}`
        cy.get(PacotePage.ADICIONAR).click()
        cy.get(PacotePage.DESCRICAO).type(nomePacote)
        cy.get(PacotePage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.contains(`${PacotePage.VEZES_POR_SEMANA} span`, 'Valor modalidade no pacote').parents('pacto-cat-form-input-number').find('input').type(valorModalidade1)
        cy.get(PacotePage.ADICIONAR_MODALIDADE).click()
        cy.get(PacotePage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('PLANO NORMAL').should('exist').click({force: true})
        cy.contains(`${PacotePage.VEZES_POR_SEMANA} span`, 'Valor modalidade no pacote').parents('pacto-cat-form-input-number').find('input').type(valorModalidade2)
        cy.get(PacotePage.ADICIONAR_MODALIDADE).click()
        cy.get(PacotePage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('IMPORTACAO').should('exist').click({force: true})
        cy.contains(`${PacotePage.VEZES_POR_SEMANA} span`, 'Valor modalidade no pacote').parents('pacto-cat-form-input-number').find('input').type(valorModalidade3)
        cy.get(PacotePage.ADICIONAR_MODALIDADE).click()
        cy.contains('span', 'Salvar').click()
        cy.contains('Pacote cadastrado com sucesso!').should('be.visible', {timeout: 10000})
        return nomePacote
    }

}

export default new PacotePageClass();
