import { HomeAdmPage } from '../../../locators';

class HomeAdmPageClass {

    alterarUnidade(unidade: string) {
        cy.wait(1000)
        cy.get(HomeAdmPage.TROCAR_UNIDADE, { timeout: 60000 }).click()
        cy.get(HomeAdmPage.MODAL_TROCAR_UNIDADE).contains(unidade).click()
        cy.get(HomeAdmPage.NOME_EMPRESA).contains(unidade)
    }

    botaoAdicionarPessoa() {
        cy.wait(2000)
        cy.get(HomeAdmPage.BOTAO_ADICIONAR_PESSOA).should('be.visible').click()
    }

    botaoConfiguracoes() {
        cy.wait(2000)
        cy.get(HomeAdmPage.BOTAO_CONFIGURACAO).should('be.visible').click()
    }
    
    validarAlunoRecente(nomeAluno) {

        cy.get(HomeAdmPage.BOTAO_ALUNOS_FAVORITOS, {timeout:3000}).should('be.visible').click()
        cy.get(HomeAdmPage.PRIMEIRO_ALUNO_RECENTE, {timeout:3000})
        .should('be.visible').contains(nomeAluno)
    }

}

export default new HomeAdmPageClass();
