import { ConvenioDescontoPage   } from '../../locators';
import { ConvenioDescontoPage } from '../../../locators';
class ConvenioDescontoPageClass {

    nomeDesconto = null;

    elements = {
        ADICIONAR: '#btn-add-item',
        BUSCA: '#input-busca-rapida',
        DESCRICAO: 'input[title="Descrição"]',
        VIGENCIA_FINAL: '[data-cy="cat-datepicker-2-input"]',
        ADICIONAR_LINHA: '#table-renderer-0-add-row',
        DESCRICAO_DURACAO: '#input-number-duracao',
        TIPO_DESCONTO: '#select-tipoDesconto',
        VALOR: '#input-decimal-valorDesconto',
    }

    gerarNomeAleatorio() {
        return 'Desconto_' + Math.random().toString(36).substring(2, 10);
    }

    cadastrarConvenioDesconto() {
        this.nomeDesconto = this.gerarNomeAleatorio();

        cy.get(ConvenioDescontoPage.ADICIONAR).click();

        cy.get(ConvenioDescontoPage.DESCRICAO)
            .click()
            .clear()
            .type(this.nomeDesconto);

        cy.get(ConvenioDescontoPage.VIGENCIA_FINAL)
            .clear()
            .type('21/11/2099');

        cy.get(ConvenioDescontoPage.ADICIONAR_LINHA).click();
        cy.get(ConvenioDescontoPage.DESCRICAO_DURACAO).type('12');
        cy.get(ConvenioDescontoPage.TIPO_DESCONTO).click();

        cy.contains('span.option-label', 'Percentual').click();

        cy.get(ConvenioDescontoPage.VALOR).type('5,00');
        cy.get('#element-0-confirm').click();

        cy.contains('Salvar').click();
        cy.contains('Convênio de desconto salvo com sucesso.').should('be.visible');
    }

    buscarConvenioDesconto(nome = this.nomeDesconto) {
        cy.intercept('GET', '**/convenio-desconto?*').as('buscarConvenio');

        cy.get(ConvenioDescontoPage.BUSCA, { timeout: 15000 })
            .clear()
            .type(nome);

        cy.wait('@buscarConvenio').its('response.statusCode').should('eq', 200);


        cy.get('#element-0 > :nth-child(1)')
            .should('be.visible')
            .click();
    }

    editarConvenioDesconto() {
        const novoNome = this.gerarNomeAleatorio();


        this.buscarConvenioDesconto();


        cy.get(ConvenioDescontoPage.DESCRICAO)
            .clear()
            .type(novoNome);

        cy.contains('Salvar').click();

        cy.contains('Convênio de desconto salvo com sucesso.').should('be.visible');

        this.nomeDesconto = novoNome;
    }

    excluirConvenioDesconto() {

        cy.intercept('GET', '**/convenio-desconto?*').as('buscarConvenio');
        cy.intercept('DELETE', '**/convenio-desconto/*').as('excluirConvenio');


        cy.get(ConvenioDescontoPage.BUSCA, { timeout: 15000 })
            .clear()
            .type(this.nomeDesconto);

        cy.wait('@buscarConvenio').its('response.statusCode').should('eq', 200);

        cy.get('.pct-more-horizontal').click();

        cy.contains('Excluir')
            .should('be.visible')
            .click();

        cy.contains('Convênio de desconto excluído com sucesso!').should('be.visible');
        cy.wait('@excluirConvenio').its('response.statusCode').should('eq', 200);



    }
}

export default new ConvenioDescontoPageClass();
