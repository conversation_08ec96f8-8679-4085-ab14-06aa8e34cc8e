import * as faker from 'faker-br'
import { CompraPage } from '../../../locators';

class CompraPageClass {
  elements = {
    CADASTRAR_NOVO: '#form\\:btnNovo',
    SELECT_FORNECEDOR: '#form\\:fornecedor',
    DESCRICAO_CONTA_PAGAR: '#form\\:descricaoContaPagar',
    NUMERO_NOTA: '#form\\:numeroNF',
    QUANTIDADE_PARCELAS: '.rich-spinner-input',
    ABA_PRODUTOS_COMPRA: '#form\\:compraItens_lbl',
    SELECT_PRODUTO_COMPRA: '#form\\:nomeProdutoSelecionado',
    QUANTIDADE: '#form\\:quantidade',
    VALOR_UNITARIO: '#form\\:valorUnitario',
    BOTAO_ADICIONAR: '#form\\:btnAdicionarProduto',
    ABA_FINANCEIRO: '#form\\:financeiroCompra_lbl',
    GRAVAR: '#form\\:salvar'
  }

  adicionarCompraComParcelas(): string {
    const nomeConta = `Conta teste ${ faker.random.number() }`
    cy.get(CompraPage.CADASTRAR_NOVO).click()
    cy.get(CompraPage.SELECT_FORNECEDOR).select('2')
    cy.get(CompraPage.DESCRICAO_CONTA_PAGAR).type(nomeConta)
    cy.get(CompraPage.NUMERO_NOTA).type('12345')
    cy.get(CompraPage.QUANTIDADE_PARCELAS).clear().type('3')
    cy.get(CompraPage.ABA_PRODUTOS_COMPRA).click()
    cy.get(CompraPage.SELECT_PRODUTO_COMPRA).type('PRODUTO SEMPRE ATIVO')
    cy.contains('PRODUTO SEMPRE ATIVO').click().wait(1500)
    cy.get(CompraPage.QUANTIDADE).should('be.visible').type('10')
    cy.get(CompraPage.VALOR_UNITARIO).clear().type('10,00')
    cy.get(CompraPage.BOTAO_ADICIONAR).click().wait(1500)
    cy.get(CompraPage.GRAVAR).click().wait(1500)
    return nomeConta
  }
}

export default new CompraPageClass();