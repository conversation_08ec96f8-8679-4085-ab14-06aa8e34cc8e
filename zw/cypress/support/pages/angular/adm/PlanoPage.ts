import EnvUtils from '@utils/EnvUtils'
import * as faker from 'faker-br'
import { PlanoPage   } from '../../locators';
import { PlanoPage } from '../../../locators';

export type PlanoType = {
    modalidade?: string,
    horario?: string,
    repeticoesNaSemana?: string,
    duracoes?: string,
    diasExtras?: string,
    valorMensal?: string,
    numeroParcelas?: string,
    quantidadeCreditos?: string,
    valorUnitario?: string
}

class PlanoPageClass {

    elements = {
        ADICIONAR: '#btn-add-plano',
        CADASTRAR_PLANO_NORMAL: '#plano-normal',
        CADASTRAR_PLANO_RECORRENTE: '#plano-recorrencia > .content',
        CADASTRAR_PLANO_CREDITO: '#plano-credito > .content',
        CADASTRAR_PLANO_PERSONAL: '#plano-personal > .content',
        NOME: '#input-plano-nome',
        NOME_2: '[data-cy="input-plano-nome-input"]',
        ADICIONAR_LINHA: '[id^="table-renderer-0-add-row"]',
        ADICIONAR_LINHA_HORARIO: '#table-renderer-2-add-row',
        SELECT_PACOTE: '#select-table-duracao-plano-pacote',
        SELECT_MODALIDADE_NORMAL: '#select-table-duracao-plano-modalidade',
        SELECT_MODALIDADE: '#select-modalidade',
        SELECT_HORARIO: '#select-table-duracao-plano-horario',
        REPETICOES_NA_SEMANA: '#input-number-table-duracao-plano-vezesSemana',
        REPETICOES_NA_SEMANA_RECORRRENTE: '#input-number-vezesSemana',
        DURACAO: '#input-number-table-duracao-plano-duracao',
        VALOR_MENSAL: '#input-decimal-table-duracao-plano-valor',
        SALVAR_DURACAO: '[id^="element-"][id$="-confirm-table-duracao-plano"]',
        SALVAR_RECORRENTE: '#element-0-confirm',
        MODELO_CONTRATO: 'pacto-cat-select-filter > #select-modelo-contrato-plano',
        BUSCA_RAPIDA: '#input-busca-rapida-plano',
        SELECT_HORARIO_RECORRENTE: '#select-horario',
        PRODUTO_TAXA_CANCELAMENTO: 'pacto-cat-select-filter > #select-produto-taxa-cancelamento-plano',
        VALOR_MENSALIDADE_RECORRENTE: '#input-valor-mensalidade',
        FIDELIDADE_PLANO_RECORRENTE: '.aux-wrapper > #input-fidelidade-plano',
        CONCLUIR_PLANO_RECORRENTE: '#btn-plano-concluir-dados-contratuais',
        ADICIONAR_LINHA_CREDITO: '#table-renderer-1-add-row-table-duracao-plano',
        DURACAO_PLANO_CREDITO: '#input-number-table-duracao-plano-numeroMeses',
        DIAS_EXTRAS: '#input-number-table-duracao-plano-quantidadeDiasExtra',
        NUMERO_PARCELAS: '#select-table-duracao-plano-nrMaximoParcelasCondPagamento',
        TIPO_HORARIO: '#select-table-duracao-plano-tipoHorarioCreditoTreino',
        VEZES_SEMANA: '#select-table-duracao-plano-numeroVezesSemana',
        QUANTIDADE_CREDITOS: '#input-number-table-duracao-plano-quantidadeCreditoCompra',
        VALOR_UNITARIO: '#input-decimal-table-duracao-plano-valorUnitario',
        CONCLUIR_PLANO_CREDITO: '#btn-plano-concluir-condicao-pagamento > .content',
        VALOR_MENSALIDADE_PERSONAL: '.aux-wrapper > #input-valor-mensalidade',
        ABA_DURACAO_VALORES: '#plano-tab-duracao-e-valores',
        EDITAR_DURACAO: '#element-0-editar-table-duracao-plano',
        ACOES: '.pct .pct-more-horizontal',
        EXCLUIR_PLANO: '#ddAction-0-excluir-plano',
        CONFIGURACOES_AVANCADAS: '#btn-advanced-config-contratual',
        HABILITAR_CONFIGURACAO_PARCELAS_DIFERENTES: '#check-gerar-parcela-valor-diferente > .cat-input-wrapper > label > .text',
        SELECT_PARCELA: '#select-config-parcelas-recorrencia-parcela',
        PARCELA_1: '#select-config-parcelas-recorrencia-parcela-0',
        INPUT_VALOR_PARCELA_AVANCADA: '#input-decimal-config-parcelas-recorrencia-valor',
        SALVAR_CONFIGURACOES: '#btn-save-config > .content',
        SALVAR_PARCELA_AVANCADA: '#element-0-confirm-config-parcelas-recorrencia',
        CONFIGURACOES_AVANCADAS_BASICOS: '#btn-advanced-config-plano > .content',
        TAB_ACESSOS_SEMANAIS: '#tab-acessos-semanais',
        ACESSO_SEMANAL: 'pacto-cat-form-select #select-qtd-max-frequencia',
        TAB_VENDAS_ONLINE: '#tab-vendas-online',
        CHECK_VENDAS_ONLINE: 'pacto-cat-checkbox #check-vendas-online-input',
        CHECK_PERMITE_COMPARTILHAR_PLANO_VENDAS_ONLINE: 'pacto-cat-checkbox #check-permite-compartilhar-plano-site-input',
        PERMITE_VENDA_BALCAO: 'pacto-cat-checkbox #check-permite-venda-plano-site-balcao-input',
        TERMO_ACEITE: 'pacto-cat-select-filter > #select-termo-aceite',
        VIDEO_YOUTUBE: 'input[placeholder="Informe aqui o código do vídeo no youtube Ex: 6AidSn00VGo"]',
        OBSERVACAO: '.ql-editor .ql-blank',
        CHECK_FLOW: 'pacto-cat-checkbox #check-pacto-flow-input',
        INPUT_DATA_CONTRATO: '#datepicker-inicio-min-contrato-input',
        TAB_CONVITES: '#tab-convites',
        QUANTIDADE_CONVITES: '#input-convidados-mes',
        TAB_DATA_PAGAMENTO: '#tab-data-pagamento',
        DATA_VENCIMENTO_OBRIGATORIO: 'pacto-cat-checkbox #check-prorata-obrigatorio-input',
        CARTAO_OBRIGATORIO: 'pacto-cat-checkbox #check-obrigatorio-informar-cartao-venda',
        TAB_RENOVACAO_AUTOMATICA: '#tab-renovacao-automatica',
        CHECK_RENOVAR_COM_DESCONTO: 'pacto-cat-checkbox #check-renovar-plano-desconto-input-input',
        CHECK_RENOVAR_PRODUTO_OBRIGATORIO: 'pacto-cat-checkbox #check-renovar-produto-obrigatorio-input',
        CHECK_RENOVAR_CONDICAO_RECORRENTE: 'pacto-cat-checkbox #check-renovar-auto-apenas-plano-cond-pag-rec-input',
        CHECK_RENOVAR_COM_VALOR_BASE: 'pacto-cat-checkbox #check-renovar-auto-valor-base-contrato-input',
        CHECK_ATIVAR_RENOVACAO_AUTOMATICA: 'pacto-cat-checkbox #check-renovavel-auto-input',
        SELECT_PLANO_RENOVACAO: 'pacto-cat-select-filter .current-value',
        TAB_PARCELA_OPERADORA: '#tab-parcela-operadora',
        CHECK_PARCELA_OPERADORA: 'pacto-cat-checkbox #check-permite-parcelamento-operadora-input',
        VEZES_DURACAO_PLANO: 'pacto-cat-checkbox #check-permite-parcelamento-operadora-duracao-input',
        INPUT_NUMERO_PARCELAS: 'pacto-cat-form-input-number .aux-wrapper input',
        TAB_COBRAR_MATRICULA_SEPARADA: '#tab-cobrar-matricula-separada',
        CHECK_COBRAR_MATRICULA_SEPARADA: 'pacto-cat-checkbox #check-permite-cobrar-adesao-separada-input',
        SELECT_PARCELAS_MATRICULA: 'pacto-cat-form-select #select-parcelar-matriculas',
        TAB_COBRAR_PRODUTO_SEPARADO: '#tab-cobrar-produtos-separado',
        CHECK_COBRAR_PRODUTO_SEPARADO: 'pacto-cat-checkbox #check-permite-cobrar-produto-separado-input',
        SELECT_PARCELAS_PRODUTO: 'pacto-cat-form-select #select-parcelar-produtos',
        TAB_COMISSAO: '#tab-comissao',
        TAB_RESTRICAO_VENDAS: '#tab-restricao-recompra',
        CHECK_COMISSAO: 'pacto-cat-checkbox #check-comissao-input',
        CHECK_RESTRICAO_VENDAS: 'pacto-cat-checkbox #check-bloquear-recompra-input',

        //EditarPlano
        BUSCA_EDITAR_PLANO_PRODUTO: '#input-busca-rapida-plano',
        DATA_EDITAR_PLANO_PRODUTO: '#datepicker-ingresso-plano-input',
        CONFIGURACOES_AVANCADAS_PRODUTO: '#btn-advanced-config-contratual > .content',
        COBRAR_PRODUTO_SEPARADO: '#tab-cobrar-produtos-separado',
        PERMITE_COBRAR_PRODUTOS_SEPARADOS: '#check-permite-cobrar-produtos-separados > .cat-input-wrapper > label > .checkmark-outline',
        PARCELAR_PRODUTOS: '.aux-parent > #select-parcelar-produtos',
        SALVAR_CONFIGURACOES_PRODUTO: '#btn-save-config > .content',

        TABELA_PLANOS: '.table-content',
        ABA_DADOS_CONTRATUAIS: '#plano-tab-dados-contratuais',
        BOTAO_APLICAR_TODOS_CONTRATOS: '.mx-auto > [data-cy="btn-advanced-config-contratual-plano"] > .content',
        CONFIRMAR_MODAL: '#action-confirmar',
        SALVAR_EDICAO: '[data-cy="plano-tabs-btn-salvar"] > .content',

        // editarPlanoRecorrenteComTaxaAdesao
        ABA_PRODUTOS_SERVICOS: '.tabs > :nth-child(4)',
        SELECT_PRODUTO: '#select-table-produto-plano-produto',
        SELECT_PRODUTO_FILTRO: '#select-table-produto-plano-produto-filter',
        SELECT_PRODUTO_FILTRADO: '#select-table-produto-plano-produto-0',
        INPUT_VALOR_PRODUTO: '#input-decimal-table-produto-plano-valorProduto',
        SALVAR_PRODUTO: '#element-4-confirm-table-produto-plano',

        //criarPlanoComObservacoes
        PLANO_NORMAL: '[data-cy="plano-normal"] > .content',
        CONFIG_AVANCADAS: '[data-cy="btn-advanced-config-plano"] > .content',
        OBSERVACOES: '.tabs > :nth-child(14)',
        INPUT_OBSERVACAO_1: '.tab-content > :nth-child(2)',
        INPUT_OBSERVACAO_2: '.tab-content > :nth-child(4)',
        SALVAR_CONFIGURACOES_2: '[data-cy="btn-save-config"] > .content',
        AVANCAR: '[data-cy="btn-plano-avancar-dados-basicos"] > .content',
        OPTION: '.options',
        AVANCAR_DURACAO: '[data-cy="btn-plano-avancar-table-duracao-valor"] > .content',
        AVANCAR_PRODUTOS: '[data-cy="btn-plano-avancar-table-produtos"] > .content',
        AVANCAR_DADOS_CONTRATUAIS: '[data-cy="btn-plano-avancar-dados-contratuais"] > .content',
        CONCLUIR: '[data-cy="btn-plano-concluir-condicao-pagamento"] > .content',
        TAXA_ADESAO: '.aux-wrapper > #input-taxa-adesao'
    }

    editarConfiguracaoPlanoCobrarProdutoSeparadamente(): void {

        cy.get(PlanoPage.BUSCA_EDITAR_PLANO_PRODUTO).type('Recorrência')
        cy.contains('PLANO RECORRÊNCIA', {timeout: 20000}).click()
        cy.contains('Dados Contratuais', {timeout: 20000}).click()
        cy.get(PlanoPage.CONFIGURACOES_AVANCADAS_PRODUTO, {timeout: 20000}).click()
        cy.get(PlanoPage.COBRAR_PRODUTO_SEPARADO, {timeout: 20000}).click()
        cy.get(PlanoPage.PERMITE_COBRAR_PRODUTOS_SEPARADOS, {timeout: 20000})
            .then(($element) => {
                if ($element.is(':visible')) {
                    cy.log('O checkbox não está marcado. Realizando o clique para marcá-lo...');
                    cy.wrap($element).click({force: true});
                    cy.log('O checkbox já está marcado. Seguindo o fluxo do teste...');
                } else {
                    cy.log('O checkbox já está marcado. Seguindo o fluxo do teste...');
                }
            });
        cy.get(PlanoPage.PARCELAR_PRODUTOS, {timeout: 20000}).select('1')
        cy.get(PlanoPage.SALVAR_CONFIGURACOES).should('be.visible').click()
        cy.contains('Salvar').click()
        cy.wait(500);
    }


    inserirModalidade(modalidade: string, horario: string, duracoes: string, valorMensal: string): void {
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get('.options').contains(modalidade ?? 'FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get('.options').contains(horario ?? 'LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).type('6')
        cy.get(PlanoPage.DURACAO).type(duracoes)
        cy.get(PlanoPage.VALOR_MENSAL).type(valorMensal)
        cy.get(PlanoPage.SALVAR_DURACAO).click()
    }

    criaPlanoNormal(props: PlanoType): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano normal ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_NORMAL).click()
        cy.get(PlanoPage.NOME).type(nomePlano, {delay: 0})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).type('6')
        cy.get(PlanoPage.DURACAO).type(props.duracoes)
        cy.get(PlanoPage.VALOR_MENSAL).type(props.valorMensal)
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Concluir').should('not.be.disabled').click()
        return nomePlano
    }

    criaPlanoNormalDuasModalidades(valorMensal1: string, valorMensal2: string): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano normal | Duas modalidades ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_NORMAL).click()
        cy.get(PlanoPage.NOME).type(nomePlano, {delay: 0})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        this.inserirModalidade('FAZ TUDO', 'LIVRE', '1', valorMensal1)
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        this.inserirModalidade('FAZ TUDO', 'HORARIO DAS 13:00 AS 17:00', '1', valorMensal2)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Concluir').should('not.be.disabled').click()
        return nomePlano
    }

    criaPlanoNormalComPacote(pacote: string, props: PlanoType): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano normal com pacote ${faker.random.number()}`
        cy.wait(1500)
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_NORMAL).click()
        cy.get(PlanoPage.NOME).type(nomePlano)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_PACOTE).click()
        cy.get('.options').contains(pacote).should('exist').click({force: true})
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.DURACAO).type(props.duracoes)
        cy.get(PlanoPage.VALOR_MENSAL).type(props.valorMensal)
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Concluir').should('not.be.disabled').click()
        return nomePlano
    }

    criaPlanoRecorrente(props: PlanoType): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano recorrente ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR, { timeout: 60000 }).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_RECORRENTE).click()
        cy.get(PlanoPage.NOME).type(nomePlano)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA_RECORRRENTE).type('7')
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA_HORARIO).click()
        cy.get(PlanoPage.SELECT_HORARIO_RECORRENTE).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.get(PlanoPage.PRODUTO_TAXA_CANCELAMENTO).click()
        cy.get('.options').contains('CUSTO ADMINISTRATIVO DO CANCELAMENTO').should('exist').click({force: true})
        cy.get(PlanoPage.VALOR_MENSALIDADE_RECORRENTE).type(props.valorMensal)
        cy.get(PlanoPage.FIDELIDADE_PLANO_RECORRENTE).type(props.duracoes)
        cy.get(PlanoPage.CONCLUIR_PLANO_RECORRENTE).click()
        return nomePlano
    }

    criaPlanoCredito(props: PlanoType): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano de credito ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR, { timeout: 60000 }).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_CREDITO).click()
        cy.get(PlanoPage.NOME).type(nomePlano)

        cy.contains('span', 'Avançar').should('not.be.disabled')
        cy.contains('span', 'Avançar').click()

        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE).click()
        cy.get('.options').contains(props.modalidade).should('be.visible').click({force: true})
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA_CREDITO).click()
        cy.get(PlanoPage.DURACAO_PLANO_CREDITO).type(props.duracoes)
        cy.get(PlanoPage.NUMERO_PARCELAS).click()
        cy.get('.options').contains(props.numeroParcelas).should('be.visible').click({force: true})
        cy.get(PlanoPage.TIPO_HORARIO).click()
        cy.get('.options').contains(props.horario).should('be.visible').click({force: true})
        cy.get(PlanoPage.QUANTIDADE_CREDITOS).type(props.quantidadeCreditos)
        cy.get(PlanoPage.VALOR_UNITARIO).type(props.valorUnitario)
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA_HORARIO).click()
        cy.get(PlanoPage.SELECT_HORARIO_RECORRENTE).should('be.visible').click()
        cy.get('.options').contains(props.horario).should('be.visible').click({force: true})
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('be.visible').click({force: true})
        cy.get(PlanoPage.PRODUTO_TAXA_CANCELAMENTO).click()
        cy.get('.options').contains('CUSTO ADMINISTRATIVO DO CANCELAMENTO').should('be.visible').click({force: true})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.CONCLUIR_PLANO_CREDITO).click()
        return nomePlano
    }

    criaPlanoPersonal(props: PlanoType): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano personal ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_PERSONAL).click()
        cy.get(PlanoPage.NOME).type(nomePlano)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.get(PlanoPage.PRODUTO_TAXA_CANCELAMENTO).click()
        cy.get('.options').contains('CUSTO ADMINISTRATIVO DO CANCELAMENTO').should('exist').click({force: true})
        cy.get(PlanoPage.VALOR_MENSALIDADE_PERSONAL).type(props.valorMensal)
        cy.get(PlanoPage.FIDELIDADE_PLANO_RECORRENTE).type(props.duracoes)
        cy.contains('span', 'Concluir').should('not.be.disabled').click()
        return nomePlano
    }

    editarPlano(plano: string, valor: string) {
        cy.intercept('GET', '**planos?filters=**').as('filterPlanos')

        cy.get(PlanoPage.BUSCA_RAPIDA, {timeout: 10000}).clear()
        cy.get(PlanoPage.BUSCA_RAPIDA).type(plano)

        cy.wait('@filterPlanos')

        cy.wait(6000);

        cy.get('.pct-more-horizontal').click().wait(300)
        cy.get('#ddAction-0-editar-plano').click()

        cy.get(PlanoPage.NOME).type(' editado')
        cy.contains('span', 'Salvar').should('not.be.disabled').click()
    }

    excluirPlano(plano: string) {
        cy.contains('.table-content', plano.toUpperCase(), {timeout: 10000})
        cy.get('.pct-more-horizontal').click().wait(300)

        cy.get(PlanoPage.EXCLUIR_PLANO).click()
    }

    acessosSemanais(vezesSemana: string) {
        cy.get(PlanoPage.TAB_ACESSOS_SEMANAIS).click()
        cy.get(PlanoPage.ACESSO_SEMANAL).should('be.visible').select(vezesSemana)
    }

    vendasOnline(participaVendas?: boolean, compartilhar?: boolean, balcao?: boolean, flow?: boolean,
                 dataInicio?: string, termoAceite?: string, videoYoutube?: string, observacao?: string) {
        cy.get(PlanoPage.TAB_VENDAS_ONLINE).click()
        if (participaVendas) cy.get(PlanoPage.CHECK_VENDAS_ONLINE).check({force: true})
        if (compartilhar) cy.get(PlanoPage.CHECK_PERMITE_COMPARTILHAR_PLANO_VENDAS_ONLINE).check({force: true})
        if (participaVendas) cy.get(PlanoPage.PERMITE_VENDA_BALCAO).check({force: true})
        if (flow) cy.get(PlanoPage.CHECK_FLOW).check({force: true})
        if (dataInicio) cy.get(PlanoPage.INPUT_DATA_CONTRATO).type(dataInicio)
        if (termoAceite) {
            cy.get(PlanoPage.TERMO_ACEITE).click()
            cy.get('.options').contains(termoAceite)
        }
        if (videoYoutube) cy.get(PlanoPage.VIDEO_YOUTUBE).type(videoYoutube)
        if (observacao) cy.get(PlanoPage.OBSERVACAO).type(observacao)
    }

    convites(numeroConvites?: string): void {
        cy.get(PlanoPage.TAB_CONVITES).click()
        if (numeroConvites) cy.get(PlanoPage.QUANTIDADE_CONVITES).type(numeroConvites)
    }

    dataPagamento(dias?: number[], vencimentoObrigatorio?: boolean, cartaoObrigatorio?: boolean) {
        cy.get(PlanoPage.TAB_DATA_PAGAMENTO).click()
        if (Array.isArray(dias) && dias.length > 0) {
            dias.forEach(dia => {
                cy.get('.host-wrapper > #select-dias-prorata').contains(dia.toString()).should('exist').parent().click()
            });
        }
        if (vencimentoObrigatorio) cy.get(PlanoPage.DATA_VENCIMENTO_OBRIGATORIO).check({force: true})
        if (cartaoObrigatorio) cy.get(PlanoPage.DATA_VENCIMENTO_OBRIGATORIO).check({force: true})
    }

    renovacaoAutomatica(renovarDesconto?: boolean, renovarProduto?: boolean, renovarCondicao?: boolean,
                        renovarValorBase?: boolean, ativarRenovacao?: boolean, plano?: string): void {
        cy.get(PlanoPage.TAB_RENOVACAO_AUTOMATICA).click()
        if (renovarDesconto) cy.get(PlanoPage.CHECK_RENOVAR_COM_DESCONTO).check({force: true})
        if (renovarProduto) cy.get(PlanoPage.CHECK_RENOVAR_PRODUTO_OBRIGATORIO).check({force: true})
        if (renovarCondicao) cy.get(PlanoPage.CHECK_RENOVAR_CONDICAO_RECORRENTE).check({force: true})
        if (renovarValorBase) cy.get(PlanoPage.CHECK_RENOVAR_COM_VALOR_BASE).check({force: true})
        if (ativarRenovacao) {
            cy.get(PlanoPage.CHECK_ATIVAR_RENOVACAO_AUTOMATICA).check({force: true})
            cy.get(PlanoPage.SELECT_PLANO_RENOVACAO).last().click()
            if (plano) cy.get('.pacto-tabs-wrapper > .tab-content').contains(plano).should('exist').click({force: true})
        }

    }

    parcelaOperadora(parcelaOperadora?: boolean, vezesDuracaoPlano?: boolean, numeroParcelas?: string): void {
        cy.get(PlanoPage.TAB_PARCELA_OPERADORA).click()
        if (numeroParcelas) {
            cy.get(PlanoPage.CHECK_PARCELA_OPERADORA).click({force: true})
            cy.get(PlanoPage.INPUT_NUMERO_PARCELAS).type(numeroParcelas)
        }
    }

    cobrarMatriculaSeparada(cobrarSeparado?: boolean, numeroParcelas?: string): void {
        cy.get(PlanoPage.TAB_COBRAR_MATRICULA_SEPARADA).click()
        if (cobrarSeparado) cy.get(PlanoPage.CHECK_COBRAR_MATRICULA_SEPARADA).check({force: true})
        if (numeroParcelas) cy.get(PlanoPage.SELECT_PARCELAS_MATRICULA).select(numeroParcelas)
    }

    cobrarProdutoSeparado(cobrarSeparado?: boolean, numeroParcelas?: string): void {
        cy.get(PlanoPage.TAB_COBRAR_PRODUTO_SEPARADO).click()
        if (cobrarSeparado) cy.get(PlanoPage.CHECK_COBRAR_PRODUTO_SEPARADO).check({force: true})
        if (numeroParcelas) cy.get(PlanoPage.SELECT_PARCELAS_PRODUTO).select(numeroParcelas)
    }

    comissao(comissao?: boolean): void {
        cy.get(PlanoPage.TAB_COMISSAO).click()
        if (comissao) cy.get(PlanoPage.CHECK_COMISSAO).check({force: true})
    }

    restricaoVendas(restricaoVendas?: boolean): void {
        cy.get(PlanoPage.TAB_RESTRICAO_VENDAS).click()
        if (restricaoVendas) cy.get(PlanoPage.CHECK_RESTRICAO_VENDAS).check({force: true})
    }

    criarPlanoComParcelasDiferentes(valorMensal: string, duracao: string, valorPrimeira: string): string {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Parcelas diferentes ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_RECORRENTE).click()
        cy.get(PlanoPage.NOME).type(nomePlano)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA_RECORRRENTE).type('7')
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA_HORARIO).click()
        cy.get(PlanoPage.SELECT_HORARIO_RECORRENTE).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.get(PlanoPage.PRODUTO_TAXA_CANCELAMENTO).click()
        cy.get('.options').contains('CUSTO ADMINISTRATIVO DO CANCELAMENTO').should('exist').click({force: true})
        cy.get(PlanoPage.VALOR_MENSALIDADE_RECORRENTE).type(valorMensal)
        cy.get(PlanoPage.FIDELIDADE_PLANO_RECORRENTE).clear().type(duracao)
        cy.get(PlanoPage.CONFIGURACOES_AVANCADAS).click()
        cy.get(PlanoPage.HABILITAR_CONFIGURACAO_PARCELAS_DIFERENTES).click()
        cy.get('div').contains('Adicionar linha').click()
        cy.get(PlanoPage.SELECT_PARCELA).click()
        cy.get(PlanoPage.PARCELA_1).click()
        cy.get(PlanoPage.INPUT_VALOR_PARCELA_AVANCADA).type(valorPrimeira)
        cy.get(PlanoPage.SALVAR_PARCELA_AVANCADA).click()
        cy.get(PlanoPage.SALVAR_CONFIGURACOES).click()
        cy.get(PlanoPage.CONCLUIR_PLANO_RECORRENTE).click()
        cy.contains('Plano salvo com sucesso.').should('be.visible')
        return nomePlano
    }

    criaPlanoComConriguracoesAvancadas(): void {
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/planos')
        const nomePlano = `Plano normal ${faker.random.number()}`
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.CADASTRAR_PLANO_NORMAL).click()
        cy.get(PlanoPage.NOME).type(nomePlano, {delay: 0})
        cy.get(PlanoPage.CONFIGURACOES_AVANCADAS_BASICOS).click()
        this.acessosSemanais('6')
        this.vendasOnline(false, false, true, false)
        this.convites('2')
        this.dataPagamento([1, 5, 9], true)
        this.renovacaoAutomatica(false, false, false, false, true)
        this.parcelaOperadora(true)
        this.cobrarMatriculaSeparada(true, '2')
        this.cobrarProdutoSeparado(true)
        this.comissao(false)
        this.restricaoVendas(true)
        cy.get(PlanoPage.SALVAR_CONFIGURACOES).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).type('6')
        cy.get(PlanoPage.DURACAO).type('1')
        cy.get(PlanoPage.VALOR_MENSAL).type('100,00')
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Concluir').should('not.be.disabled').click()
        cy.contains('Plano salvo com sucesso.').should('be.visible')
    }

    mudarVigenciaPlano(): void {
        cy.intercept('GET', '**planos?filters=**').as('filterPlanos')
        cy.get(PlanoPage.BUSCA_EDITAR_PLANO_PRODUTO).type('Recorrência')
        cy.wait('@filterPlanos')
        cy.contains('PLANO RECORRÊNCIA').click()
        cy.get(PlanoPage.DATA_EDITAR_PLANO_PRODUTO).type('20/05/2099')
        cy.contains('Salvar').click()
    }

    alterarModeloContrato(plano: string, modelo: string): void {
        cy.get(PlanoPage.BUSCA_RAPIDA).clear().type(plano).wait(1500)
        cy.get(PlanoPage.TABELA_PLANOS).contains(plano.toUpperCase()).click()
        cy.get(PlanoPage.ABA_DADOS_CONTRATUAIS).click()
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains(modelo).should('exist').click({force: true})
        cy.get(PlanoPage.BOTAO_APLICAR_TODOS_CONTRATOS).click().wait(1000)
        cy.get(PlanoPage.CONFIRMAR_MODAL).click()
        cy.contains('Texto aplicado para contratos já lançados').should('be.visible')
    }

    editarPlanoRecorrenteComTaxaAdesao(nomePlano: string, valorAdesao: string): void {
        cy.intercept('GET', '**planos?filters=**').as('filterPlanos')

        cy.get(PlanoPage.BUSCA_RAPIDA, {timeout: 10000}).clear()
        cy.get(PlanoPage.BUSCA_RAPIDA).type(nomePlano)

        cy.wait('@filterPlanos')

        cy.wait(3000);

        cy.get('#ddaction-0-plano').click()
        cy.get('#ddAction-0-editar-plano').click()

        cy.get(PlanoPage.ABA_PRODUTOS_SERVICOS).click()
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_PRODUTO).click()
        cy.get(PlanoPage.SELECT_PRODUTO_FILTRO).type('ADESÃO', {delay: 0}).wait(1000)
        cy.get(PlanoPage.SELECT_PRODUTO_FILTRADO).click().wait(750)
        cy.get(PlanoPage.INPUT_VALOR_PRODUTO).type('150,00')
        cy.get(PlanoPage.SALVAR_PRODUTO).click()
        cy.contains('span', 'Salvar').should('not.be.disabled').click()
    }

    criarPlanoComObservacoes(): string {
        const nomePlano = `Plano com observação ${faker.random.number()}`

        // Tela: Planos
        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.PLANO_NORMAL).click()

        // Aba Dados Básicos
        cy.get(PlanoPage.NOME_2).type(nomePlano)
        cy.get(PlanoPage.CONFIG_AVANCADAS).click()
        cy.get(PlanoPage.OBSERVACOES).click()
        cy.get(PlanoPage.INPUT_OBSERVACAO_1).type('Observações 1')
        cy.get(PlanoPage.INPUT_OBSERVACAO_2).type('Observações 2')
        cy.get(PlanoPage.SALVAR_CONFIGURACOES_2).click()

        cy.get(PlanoPage.AVANCAR).click()

        // Aba Duração e Valores
        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get(PlanoPage.OPTION).contains('FAZ TUDO').click()
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get(PlanoPage.OPTION).contains('LIVRE').click({ force: true })
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).clear().type('7')
        cy.get(PlanoPage.DURACAO).clear().type('12')
        cy.get(PlanoPage.VALOR_MENSAL).clear().type('150,00')
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.get(PlanoPage.AVANCAR_DURACAO).click()

        // Aba Produtos e Serviços
        cy.get(PlanoPage.AVANCAR_PRODUTOS).click()

        // Aba Dados Contratuais
        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get(PlanoPage.OPTION).contains('NOME DA EMPRESA | CPS 2019').click({ force: true })
        cy.get(PlanoPage.AVANCAR_DADOS_CONTRATUAIS).click()
        cy.get(PlanoPage.CONCLUIR).click()
        cy.contains('Plano salvo com sucesso.').should('be.visible')

        return nomePlano
    }

    buscaPlano(nomePlano: string): void {
        cy.get(PlanoPage.BUSCA_RAPIDA).clear().type(nomePlano)
        cy.get(PlanoPage.TABELA_PLANOS).contains(nomePlano.toUpperCase(), {timeout: 10000}).click()
    }

    validaTextoObservacao(nomePlano: string, obs1: string, obs2: string): void {
        this.buscaPlano(nomePlano)
        cy.get(PlanoPage.CONFIG_AVANCADAS).click()
        cy.get(PlanoPage.OBSERVACOES).click()
        cy.get(PlanoPage.INPUT_OBSERVACAO_1).should('have.value', obs1)
        cy.get(PlanoPage.INPUT_OBSERVACAO_2).should('have.value', obs2)

    }

    incluirPlanoVisitante(): string {
        const nomePlano = `Plano Visitante ${Date.now()}`

        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.PLANO_NORMAL).click()

        cy.get(PlanoPage.NOME_2).type(nomePlano)
        cy.get(PlanoPage.CONFIG_AVANCADAS).click()
        this.restricaoVendas(true)
        cy.get(PlanoPage.SALVAR_CONFIGURACOES_2).click()
        cy.get(PlanoPage.AVANCAR).click()

        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get(PlanoPage.OPTION).contains('FAZ TUDO').click()
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get(PlanoPage.OPTION).contains('LIVRE').click({ force: true })
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).clear().type('7')
        cy.get(PlanoPage.DURACAO).clear().type('12')
        cy.get(PlanoPage.VALOR_MENSAL).clear().type('150,00')
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.get(PlanoPage.AVANCAR_DURACAO).click()

        cy.get(PlanoPage.AVANCAR_PRODUTOS).click()

        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get(PlanoPage.OPTION).contains('NOME DA EMPRESA | CPS 2019').click({ force: true })
        cy.get(PlanoPage.AVANCAR_DADOS_CONTRATUAIS).click()
        cy.get(PlanoPage.CONCLUIR).click()
        cy.contains('Plano salvo com sucesso.').should('be.visible')

        return nomePlano
    }

    criarPlanoComTurma(): string {
        const nomePlano = `Plano com turma ${faker.random.number()}`

        cy.get(PlanoPage.ADICIONAR).click()
        cy.get(PlanoPage.PLANO_NORMAL).click()

        cy.get(PlanoPage.NOME_2).type(nomePlano)
        cy.get(PlanoPage.AVANCAR).click()

        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE_NORMAL).click()
        cy.get(PlanoPage.OPTION).contains('TESTE AUTO TURMA').click()
        cy.get(PlanoPage.SELECT_HORARIO).click()
        cy.get(PlanoPage.OPTION).contains('HORARIO DA TURMA').click({ force: true })
        cy.get(PlanoPage.REPETICOES_NA_SEMANA).clear().type('2')
        cy.get(PlanoPage.DURACAO).clear().type('12')
        cy.get(PlanoPage.VALOR_MENSAL).clear().type('150,00')
        cy.get(PlanoPage.SALVAR_DURACAO).click()
        cy.get(PlanoPage.AVANCAR_DURACAO).click()

        cy.get(PlanoPage.AVANCAR_PRODUTOS).click()

        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get(PlanoPage.OPTION).contains('NOME DA EMPRESA | CPS 2019').click({ force: true })
        cy.get(PlanoPage.AVANCAR_DADOS_CONTRATUAIS).click()
        cy.get(PlanoPage.CONCLUIR).click()
        cy.contains('Plano salvo com sucesso.', { timeout: 60000 }).should('be.visible')

        return nomePlano;
    }

    criarPlanoRecorrenteComAdesao(): string {
        const nomePlano = `Plano com Adesão ${faker.random.number()}`

        cy.wait(2000)
        cy.get(PlanoPage.ADICIONAR).click() 
        cy.get(PlanoPage.CADASTRAR_PLANO_RECORRENTE).click()

        cy.get(PlanoPage.NOME).type(nomePlano)
        cy.contains('span', 'Avançar').should('not.be.disabled').click()

        cy.get(PlanoPage.ADICIONAR_LINHA).click()
        cy.get(PlanoPage.SELECT_MODALIDADE).click()
        cy.get('.options').contains('FAZ TUDO').should('exist').click({force: true})
        cy.get(PlanoPage.REPETICOES_NA_SEMANA_RECORRRENTE).type('7')
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()

        cy.get(PlanoPage.ADICIONAR_LINHA_HORARIO).click()
        cy.get(PlanoPage.SELECT_HORARIO_RECORRENTE).click()
        cy.get('.options').contains('LIVRE').should('exist').click({force: true})
        cy.get(PlanoPage.SALVAR_RECORRENTE).click()

        cy.contains('span', 'Avançar').should('not.be.disabled').click()
        cy.contains('span', 'Avançar').should('not.be.disabled').click()

        cy.get(PlanoPage.MODELO_CONTRATO).click()
        cy.get('.options').contains('NOME DA EMPRESA | CPS 2019').should('exist').click({force: true})
        cy.get(PlanoPage.PRODUTO_TAXA_CANCELAMENTO).click()
        cy.get('.options').contains('CUSTO ADMINISTRATIVO DO CANCELAMENTO').should('exist').click({force: true})
        cy.get(PlanoPage.VALOR_MENSALIDADE_RECORRENTE).type('150,00')
        cy.get(PlanoPage.TAXA_ADESAO).type('100,00').wait(1000)
        cy.get(PlanoPage.CONCLUIR_PLANO_RECORRENTE).click()
        
        return nomePlano;
    }

}

export default new PlanoPageClass();
