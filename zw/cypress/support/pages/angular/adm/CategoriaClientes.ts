import { CategoriaClientesPage } from '../../../locators';
class CategoriaClientesClassPage {
    elements = {
        ADICIONAR: '#btn-add-item',
        NOME: 'input[placeholder="Nome"]',
        BUSCA: '#input-busca-rapida',
        EXCLUIR: '#element-0-action-delete\\ \\(key\\)'

    }

    adicionarCategoria() {

        cy.get(CategoriaClientesPage.ADICIONAR).click()
        cy.get(CategoriaClientesPage.NOME).eq(0).click().type('TESTE AUTO')

        cy.get('select')
            .should('be.visible')
            .select('CO')
            .should('have.value', 'CO');

        cy.contains('Salvar').click()
        cy.contains('Categoria de clientes cadastrada com sucesso!').should('be.visible')

    }

    editarCategoria() {


        cy.get(CategoriaClientesPage.BUSCA, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('TESTE AUTO');

        cy.contains('TESTE AUTO').click();
        cy.get(CategoriaClientesPage.NOME)
            .eq(0)
            .clear()
            .type('BRINDE TESTE AUTO 02')

        cy.contains('Salvar').click()
        cy.contains('Categoria de clientes cadastrada com sucesso!').should('be.visible')



    }

    excluirCategoria() {

        cy.get(CategoriaClientesPage.BUSCA, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('TESTE AUTO 02');


        cy.contains('TESTE AUTO 02').should('be.visible')

        cy.get(CategoriaClientesPage.EXCLUIR, { timeout: 15000 }).click()
        cy.contains('Categoria excluída com sucesso.').should('be.visible')

    }
}

export default new CategoriaClientesPage();