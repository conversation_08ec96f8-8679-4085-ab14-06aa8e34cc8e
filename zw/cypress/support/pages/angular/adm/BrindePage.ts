import { BrindePage } from '../../../locators';
class BrindePageClass {

    elements = {
        ADICIONAR: '#btn-add-item',
        NOME: '[title="Nome"]',
        VALOR_PONTOS: 'input[placeholder="0000"]',
        EMPRESA: 'input[placeholder="Empresa"]',
        ATIVO:'.checkmark-outline',
        BUSCA: '#input-busca-rapida',
        EXCLUIR:'#element-0-action-delete\\ \\(key\\)'


    }
    adicionarBrinde() {

        cy.get(BrindePage.ADICIONAR).click()
        cy.get(BrindePage.NOME).click().type('BRINDE TESTE AUTO')
        cy.get(BrindePage.VALOR_PONTOS).type('100')

        cy.get('.current-wrapper')
            .contains('-')
            .click();
        cy.get('.options').should('be.visible');
        cy.get('.option-label')
            .contains('NOME FANTASIA DA ACADEMIA')
            .click();
        cy.get('.current-wrapper .option-label')
            .should('contain', 'NOME FANTASIA DA ACADEMIA');

        cy.get(BrindePage.ATIVO).click();

        cy.contains('Salvar').click()
        cy.contains('Brinde cadastrado com sucesso!').should('be.visible')
    }
     editarBrinde() {

        cy.get(BrindePage.BUSCA, { timeout: 15000 })
        .should('be.visible')
        .click()
        .type('BRINDE TESTE AUTO');

        cy.contains('BRINDE TESTE AUTO').click();
        cy.get(BrindePage.NOME).clear().type('BRINDE TESTE AUTO 02')
        cy.contains('Salvar').click()
        cy.contains('Brinde cadastrado com sucesso!').should('be.visible')

     }

     excluirBrinde() {
        cy.get(BrindePage.BUSCA, { timeout: 15000 })
        .should('be.visible')
        .click()
        .type('BRINDE TESTE AUTO 02');


        cy.contains('BRINDE TESTE AUTO 02') .should('be.visible')

        cy.get(BrindePage.EXCLUIR, { timeout: 15000 }).click()
        cy.contains('Brinde excluído com sucesso.').should('be.visible')
 }
}

export default new BrindePageClass();