import DateUtils from "@utils/DateUtils"
import { FreepassPage } from '../../../locators';

class FreepassPageClass {
  elements = {
    FREE_PASS: '[data-cy="FREE_PASS"]',
    INPUT_QUANTIDADE_DIAS: 'input[title="Qtd. dias do Freepass"]',
    CLIENTE: '#select-codigo-cliente',
    INPUT_CLIENTE: 'input[type="search"]',
    ALUNO_SELECIONADO: '#select-codigo-cliente-0',
    SELECT_PRODUTO: '#select-codigo-produto',
    DATA_INICIO: '.wrapper > .ng-untouched',
    OPTIONS: '.ds3-select-options',
    CALENDARIO_DATA_LANCAMENTO: '.mat-calendar-content',
    ICONE_CALENDARIO: '.wrapper > button',
    EXCLUIR_FREEPASS: '#btn-freepass-excluir-antigo'
  }

  lancaFreepass(nomeAluno: string) {
    cy.get(FreepassPage.FREE_PASS, { timeout: 20000 }).click()
    cy.get(FreepassPage.CLIENTE).click()
    cy.get(FreepassPage.INPUT_CLIENTE).first().type(nomeAluno, { delay: 0 }).wait(1000)
     cy.get(FreepassPage.OPTIONS).contains(nomeAluno.toLowerCase()).should('be.visible').click()
    cy.get(FreepassPage.SELECT_PRODUTO).click().wait(1000)
    cy.get(FreepassPage.OPTIONS).contains('1 DIA DE AULA EXPERIMENTAL').should('be.visible').click()
    cy.contains('button', 'Confirmar').click()
    cy.contains('Freepass lançado com sucesso!').should('be.visible').click()
  }

  removerFreepass(nomeAluno: string) {
     cy.get(FreepassPage.CLIENTE).click()
    cy.get(FreepassPage.INPUT_CLIENTE).first().clear().type(nomeAluno, { delay: 0 }).wait(1000)
    cy.get(FreepassPage.OPTIONS).contains(nomeAluno.toLowerCase()).should('be.visible').click()
    cy.get(FreepassPage.EXCLUIR_FREEPASS).click()
    cy.contains('Freepass excluído com sucesso!').should('be.visible').click()
  }

  lancaFreepassRetroativo(nomeAluno: string) {
    cy.get(FreepassPage.ICONE_CALENDARIO).click()
    cy.get(FreepassPage.CALENDARIO_DATA_LANCAMENTO).contains('1').click()
    cy.get(FreepassPage.CLIENTE).click()
    cy.get(FreepassPage.INPUT_CLIENTE).first().type(nomeAluno, { delay: 0 }).wait(1000)
    cy.get(FreepassPage.OPTIONS).contains(nomeAluno.toLowerCase()).should('be.visible').click()
    cy.get(FreepassPage.SELECT_PRODUTO).click().wait(1000)
    cy.get(FreepassPage.OPTIONS).contains('1 DIA DE AULA EXPERIMENTAL').should('be.visible').click()
    cy.contains('button', 'Confirmar').click()
    cy.contains('Freepass lançado com sucesso!').should('be.visible').click()
  }

}

export default new FreepassPageClass()