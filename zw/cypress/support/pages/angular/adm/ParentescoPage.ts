import { ParentescoPage } from '../../../locators';
class ParentescoPageClass {
  elements = {
    PARENTESCO: 'input[placeholder="Amigo"]',
    ADICIONAR: '#btn-add-item',
    IDADE: 'pacto-cat-form-input-number input',
    BUSCA: '#input-busca-rapida',
    RESULTADO: '[id^=element-0-action-edit]',
    DELETE: '[id^=element-0-action-delete]'
  }

 parentescoSelecionado = ''
  novoParentesco = ''

  adicionarParentesco() {
    const parentescos = ["Amigo", "Irmão", "Tio", "Primo", "Sobrinho", "Av<PERSON>", "Vizinho"]
    this.parentescoSelecionado = parentescos[Math.floor(Math.random() * parentescos.length)]

    cy.get(ParentescoPage.ADICIONAR).click()
    cy.get(ParentescoPage.PARENTESCO).click().type(this.parentescoSelecionado)
    cy.get(ParentescoPage.IDADE).eq(1).type('99')
    cy.contains('span', 'Salvar').click()
    cy.contains('Parentesco cadastrado com sucesso!').should('be.visible')
  }

  editarParentesco() {
    
    this.novoParentesco = `${this.parentescoSelecionado}-Editado`
    cy.get(ParentescoPage.BUSCA).type(this.parentescoSelecionado)
    cy.wait(2000)
    cy.get(ParentescoPage.RESULTADO).click()

    cy.get(ParentescoPage.PARENTESCO).click().clear().type(this.novoParentesco)
    cy.contains('span', 'Salvar').click()
    cy.contains('Parentesco cadastrado com sucesso!').should('be.visible')
  }

  excluirParentesco() {
    cy.intercept('DELETE', '**parentesco/*').as('deleteParentesco')
    cy.get(ParentescoPage.BUSCA).clear().type(this.novoParentesco)
    cy.wait(2000)
    cy.get(ParentescoPage.DELETE).click()
    cy.contains('Parentesco excluido com sucesso!').should('be.visible')
    cy.wait('@deleteParentesco').its('response.statusCode').should('eq', 200)
    
 }
}

export default new ParentescoPageClass();