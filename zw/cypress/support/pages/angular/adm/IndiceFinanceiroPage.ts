import { IndiceFinanceiroPage } from '../../../locators';
class IndiceFinanceiroPageClass {
    elements = {
        BOTAO_ADICIONAR: '#btn-add-item > .content',
        DADOS: '[class="option-label"]',
        ANO: '[class="aux-wrapper asas"]',
        TABELA_RELATORIO: '.table-content',
        MENSAGEM_RETORNO: '.snotifyToast__body',
        BOTAO_DELETAR: '#element-0-deleteindice'

    }

    cadastrarIndiceFinanceiro(): void {

        cy.wait(5000)
        cy.contains('Índice financeiro', {timeout: 15000}).should('be.visible')
        cy.get(IndiceFinanceiroPage.BOTAO_ADICIONAR, {timeout: 15000}).should('be.visible').click()
        cy.contains('Insira os dados', {timeout: 15000}).should('be.visible')
        cy.get(IndiceFinanceiroPage.DADOS).should('be.visible').eq(0).click()
        cy.contains('Índice outros').should('be.visible').click()
        cy.get(IndiceFinanceiroPage.DADOS).should('be.visible').eq(1).click()
        cy.contains('Janeiro').should('be.visible').click()
        cy.get(IndiceFinanceiroPage.ANO).should('be.visible').eq(0).type('2055')
        cy.get(IndiceFinanceiroPage.ANO).should('be.visible').eq(1).type('2000')
        cy.contains('Salvar', {timeout: 15000}).should('be.visible').click()
        cy.get(IndiceFinanceiroPage.MENSAGEM_RETORNO, {timeout: 15000}).should('be.visible').should('contain', 'Índice financeiro cadastrado com sucesso!')
        cy.get(IndiceFinanceiroPage.TABELA_RELATORIO, {timeout: 15000}).should('be.visible').should('contain', '2055')
        cy.get(IndiceFinanceiroPage.TABELA_RELATORIO, {timeout: 15000}).should('be.visible').should('contain', 'Janeiro')

    }

    deletarIndiceFinanceiro(anoIndice: string): void {
        cy.wait(500)
        cy.get(IndiceFinanceiroPage.BOTAO_DELETAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(5000)
        cy.get(IndiceFinanceiroPage.TABELA_RELATORIO, {timeout: 15000}).should('be.visible').should('not.contain', anoIndice);

    }

}


export default new IndiceFinanceiroPageClass();
