import { DescontoPage   } from '../../locators';
import { DescontoPage } from '../../../locators';
class DescontoPageClass {
    elements = {
        ADICIONAR: '#btn-add-plano-desconto',
        DESCRICAO: '[data-cy="plano-novo-desconto-descricao-input"]',
        TIPO_PRODUTO: '[data-cy="plano-novo-desconto-tipo-produto"]',
        ATIVO: '#plano-novo-desconto-ativo > .cat-input-wrapper > label > .checkmark-outline',
        TIPO_DESCONTO: '.aux-parent > #plano-novo-desconto-tipo-desconto',
        VALOR: '.aux-wrapper > #plano-novo-desconto-tipo-desconto-valor',

    }

    cadastrarDescontoPadraoVendaAvulsa() {
        cy.get(DescontoPage.ADICIONAR).click()
        cy.get(DescontoPage.DESCRICAO).type('TESTE AUTO')
        cy.get(DescontoPage.TIPO_PRODUTO).select('Produto estoque')
        cy.get(DescontoPage.ATIVO).click()
        cy.get(DescontoPage.TIPO_DESCONTO).select('Valor')
        cy.get(DescontoPage.VALOR).type('5,00')
        cy.contains('Salvar').click()
        cy.contains('Desconto cadastrado com sucesso!').should('be.visible')
    }

}

export default new DescontoPageClass();