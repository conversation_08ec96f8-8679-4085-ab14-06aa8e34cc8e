import { TipoDePlanoPage } from '../../../locators';
class TipoDePlanoPageClass {
    elements = {
        ADICIONAR:'#btn-add-item',
        NOME:'[title="Nome"]',
        TIPO:'[title="Tipo"]',
        ATIVO: '.checkmark-outline',
        BUSCA: '#input-busca-rapida',

    }

    adicionarTipoPlano() {
        cy.get(TipoDePlanoPage.ADICIONAR).click()
        cy.get(TipoDePlanoPage.NOME).click().type('teste auto');
        cy.get(TipoDePlanoPage.TIPO).click().type('QA');
        cy.get(TipoDePlanoPage.ATIVO).click()
        cy.contains('Salvar').click()
        cy.contains('Tipo De Plano cadastrado com sucesso').should('be.visible')

 }
  editarTipoPlano() {

    cy.get(TipoDePlanoPage.BUSCA, { timeout: 15000 })
        .should('be.visible')
        .click()
        .type('teste auto');

        cy.contains('teste auto').click();
        cy.get(TipoDePlanoPage.NOME, { timeout: 15000 }).clear().click().type('teste auto 02');

        cy.contains('Salvar').click()
        cy.contains('Tipo De Plano cadastrado com sucesso').should('be.visible')



}
}


    export default new TipoDePlanoPageClass();