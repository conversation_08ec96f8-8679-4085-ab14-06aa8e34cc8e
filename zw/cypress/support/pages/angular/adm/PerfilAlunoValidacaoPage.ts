import { ClientePessoa } from '@models/Cliente';
import DateUtils from '@utils/DateUtils';
import { PerfilAlunoValidacaoPage } from '../../../locators';
class PerfilAlunoValidacaoPageClass {
    elements = {

        TREINO: '#module-ntr',
        RESULTADO_BUSCA: '#topbar-search-field',

        ABA_FINANCEIRO: '#pcc-tab-financeiro',
        DESCONTO_PERFIL_DO_ALUNO: '#element-0-pch-fin-table-compras > :nth-child(4) > .column-cell',
        DESCONTO_VALOR_PERFIL_DO_ALUNO: '#element-0-pch-fin-table-compras > :nth-child(9)',
        INICIO: ':nth-child(5) > .column-cell > .ng-star-inserted',
        SITUACAO_ATIVO: '.situacao-aluno.primario.at',
        ABA_PRODUTO: '#pcc-tab-produto',


    }

    validarSituacaoAtivo() {
        cy.contains('Ativo').should('be.visible')
    }

    validarDescontoExtraContrato(aluno: string, valorDesconto: string): void {
        cy.wait(5000);
        cy.get(PerfilAlunoValidacaoPage.ABA_FINANCEIRO, { timeout: 60000 }).click()
        cy.get(PerfilAlunoValidacaoPage.DESCONTO_PERFIL_DO_ALUNO)
            .should('contain', 'Desconto Extra');
        cy.get(PerfilAlunoValidacaoPage.DESCONTO_VALOR_PERFIL_DO_ALUNO)
            .should('contain', valorDesconto);
    }


    validarContratoComDataAlterada(aluno: string, data: string): void {

        cy.wait(500);

        cy.get('#module-ntr > .pct', {timeout: 15000}).click()
        cy.get('#topbar-search-field', { timeout: 15000 }).type(aluno);
        cy.contains(aluno).click();
        cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste').as('ultimaRequisicao');
        cy.wait('@ultimaRequisicao', { timeout: 50000 });
        cy.get(PerfilAlunoValidacaoPage.INICIO)
            .should('have.text', data);

    }

    validarContrato(aluno: string): void {
        cy.get('#module-ntr > .pct').click()
        cy.get('#topbar-search-field').type(aluno);
        cy.contains(aluno).click();

        cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste').as('ultimaRequisicao');
        cy.wait('@ultimaRequisicao', { timeout: 50000 });
        cy.get(PerfilAlunoValidacaoPage.SITUACAO_ATIVO, { timeout: 20000 }).should('be.visible');
    }


    validarPlanoEDividirProdutosNasParcelas(aluno: string): void {

        cy.get('#module-ntr', { timeout: 15000 }).click()
        cy.get('#topbar-search-field').type(aluno);
        cy.contains(aluno).click();
        cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste').as('ultimaRequisicao');
        cy.wait('@ultimaRequisicao', { timeout: 50000 });
        cy.contains('Ativo', { timeout: 50000 }).should('be.visible')
        cy.get(PerfilAlunoValidacaoPage.ABA_FINANCEIRO).click()

        cy.contains('Histórico de Parcelas').trigger('mouseover');
        cy.get('#element-2-tbl-prod-cobrados-parcela > :nth-child(7)')
            .invoke('text')
            .then((text) => {
                const normalizedText = text.replace(/\s/g, ''); // Remove TODOS os espaços invisíveis
                expect(normalizedText).to.eq('R$93,33'); // Compara sem espaços extras
            });


        cy.wait(500);

    }

    validarProdutoVendidoVendaAvulsa(aluno: ClientePessoa) {

        cy.get(PerfilAlunoValidacaoPage.TREINO).should('be.visible').click()
        cy.get(PerfilAlunoValidacaoPage.RESULTADO_BUSCA, { timeout: 15000 }).type(aluno.pessoa.nome);
        cy.contains(aluno.pessoa.nome).click();
        cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste').as('ultimaRequisicao');
        cy.wait('@ultimaRequisicao', { timeout: 50000 });
        cy.get(PerfilAlunoValidacaoPage.ABA_PRODUTO).click();
        cy.contains('Vitaminas').should('be.visible')



    }
}

export default new PerfilAlunoValidacaoPageClass();
