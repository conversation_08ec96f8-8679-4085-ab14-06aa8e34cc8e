import { CampanhaPage   } from '../../locators';
import { CampanhaPage } from '../../../locators';
class CampanhaPageClass {
    elements = {
        CADASTRAR_CAMPANHA: '#form\\:btnCadastrarCampanha',
        NOME: '#formEditar\\:nomeBrinde',
        DATA_INICIO: '#formEditar\\:dataInicialInputDate',
        DATA_FIM: '#formEditar\\:dataFinalInputDate',
        DESCRICAO: '#formEditar\\:descricao',
        CATEGORIA: '#formEditar\\:tblComPontos\\:0\\:multipicadorCategoria',
        SALVAR: '#formEditar\\:salvar',
        EDITAR: '#form\\:listaCampanha\\:0\\:btnEditar',
        EXCLUIR: '#form\\:listaCampanha\\:0\\:btnExcluir1',
        SIM: '#formAviso\\:confirmacaoExclusaoCampanha'
    }

    adicionarCampanha() {
        cy.get(CampanhaPage.CADASTRAR_CAMPANHA).click();
        cy.get(CampanhaPage.NOME, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('Campanha Teste Auto')


        const hoje = new Date();
        hoje.setDate(hoje.getDate() + 3); // Soma 3 dias

        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0'); // Mês começa do zero
        const ano = hoje.getFullYear();

        const dataTrancamento = `${dia}/${mes}/${ano}`;

        cy.get(CampanhaPage.DATA_INICIO)
            .should('be.visible')
            .clear()
            .type(dataTrancamento);

        hoje.setDate(hoje.getDate() + 1); // Soma 1 dia para DATA_FIM

        const diaFim = String(hoje.getDate()).padStart(2, '0');
        const mesFim = String(hoje.getMonth() + 1).padStart(2, '0');
        const anoFim = hoje.getFullYear();

        const dataFim = `${diaFim}/${mesFim}/${anoFim}`;

        cy.get(CampanhaPage.DATA_FIM)
            .should('be.visible')
            .clear()
            .type(dataFim);

        cy.get(CampanhaPage.DESCRICAO)
            .should('be.visible')
            .clear()
            .type('TESTE AUTO')

        cy.get(CampanhaPage.CATEGORIA)
            .should('be.visible')
            .clear()
            .type('40')

        cy.get(CampanhaPage.SALVAR).click()
        cy.contains('Operação Realizada com sucesso!').should('be.visible')


        cy.get('#hidelink', { timeout: 15000 }).click()



    }

    editarCampanha() {

        cy.get(CampanhaPage.EDITAR, { timeout: 15000 })
            .should('be.visible')
            .click()

        cy.get(CampanhaPage.DESCRICAO)
            .should('be.visible')
            .clear()
            .type('TESTE AUTO 02')

        cy.get(CampanhaPage.SALVAR).click()

        cy.contains('Operação Realizada com sucesso!').should('be.visible')
    }

    excluirCampanha() {

        cy.get(CampanhaPage.EXCLUIR, { timeout: 15000 })
            .should('be.visible')
            .click()

        cy.get(CampanhaPage.SIM, { timeout: 15000 })
            .should('be.visible')
            .click()

        cy.contains('Campanha excluida com Sucesso!').should('be.visible')

    }
}

export default new CampanhaPageClass();