import { AmbientePage } from '../../../locators';
class AmbientePageClass {

    elements = {
        CADASTRAR_NOVO: '#form\\:btnNovo',
        DESCRICAO_AMBIENTE: '#form\\:descricao',
        CAPACIDADE: '#form\\:capacidade',
        TIPO_AMBIENTE: '#form\\:tiposAmbiente',
        COLETOR: '#form\\:coletor',
        SITUACAO: '#form\\:situacao',
        SALVAR:'#form\\:salvar',
        VOLTAR: '#form\\:consultar'
    }
    adicionarAmbiente() {
        cy.wait(5000);
        const descricaoAleatoria = `Teste Auto ${Date.now()}`;
        cy.get(AmbientePage.CADASTRAR_NOVO).click();
        cy.get(AmbientePage.DESCRICAO_AMBIENTE).type(descricaoAleatoria);
        cy.get(AmbientePage.CAPACIDADE).type('5');
        cy.get(AmbientePage.TIPO_AMBIENTE).select('Turma');
        cy.get(AmbientePage.COLETOR).select('NOME EMPRESA | USO DA PACTO');
        cy.get(AmbientePage.SITUACAO).select('Ativo');
    
        cy.wait(300);
        cy.get(AmbientePage.SALVAR, { timeout: 10000 }).should('be.visible').click();
        cy.contains('Dados Gravados com Sucesso').should('be.visible');
        cy.get(AmbientePage.VOLTAR).should('be.visible').click();
    }
    
    editarAmbiente() {
        cy.wait(5000);
        cy.contains('SALA DE GINASTICA', { timeout: 10000 }).should('be.visible').click();
        cy.get(AmbientePage.CAPACIDADE).clear().type('40');
        cy.get(AmbientePage.SALVAR).click(); 
        cy.contains('Dados Gravados com Sucesso').should('be.visible');
    }
    
}
export default new AmbientePageClass();