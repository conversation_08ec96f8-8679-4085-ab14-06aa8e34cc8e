import { ClassificacaoPage } from '../../../locators';
class ClassificacaoPageClass {

    elements = {
        ADICIONAR_CLASSIFICACAO: '#btn-add-item',
        NOME: 'input[placeholder="Nome"]',
        EDITAR: '#element-0-action-edit\\ \\(key\\)',
        BUSCA: '#input-busca-rapida',
        EXCLUIR: '#element-0-action-delete\\ \\(key\\)',

    }

    adicionarClassificacao(): void {
        cy.get(ClassificacaoPage.ADICIONAR_CLASSIFICACAO).click()
        cy.get(ClassificacaoPage.NOME)
            .click()
            .type('TESTE ALANIS');

        cy.contains('Salvar').click();
        cy.contains('Classificação cadastrada com sucesso!').should('be.visible')


    }

    editarClassificacao(): void {
        this.buscarClassificacao('TESTE ALANIS')

        cy.get(ClassificacaoPage.EDITAR, {timeout: 15000})
            .should('exist')
            .and('be.visible')
            .and('not.be.disabled')
            .click({force: true});

        cy.get(ClassificacaoPage.NOME)
            .click()
            .clear()
            .type('TESTE ALANIS QA');
        cy.contains('Salvar').click();
        cy.contains('Classificação alterada com sucesso!').should('be.visible')
    }

    excluirClassificacao(): void {
        this.buscarClassificacao('TESTE ALANIS QA')
        cy.get(ClassificacaoPage.EXCLUIR, {timeout: 15000}).click();
        cy.contains('Cadastro excluído com sucesso.')
        // Verifica que não existe mais nenhum elemento com a descrição QA
        cy.contains('TESTE ALANIS QA').should('not.exist');
    }

    private buscarClassificacao(nome: string): void {
        cy.get(ClassificacaoPage.BUSCA, {timeout: 15000}).should('be.visible')
        cy.get(ClassificacaoPage.BUSCA).type(nome);
        cy.intercept('GET', '**/classificacao?filters=**').as('findClassificacao');
        cy.wait('@findClassificacao').its('response.statusCode').should('eq', 200)
    }
}

export default new ClassificacaoPageClass();
