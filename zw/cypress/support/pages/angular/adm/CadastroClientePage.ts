import { CadastroClientePage } from '../../../locators';

class CadastrarClientePage {
    nomeCadastrado = '';

    gerarNomeAleatorio() {
        const nomes = ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'];
        const sobrenomes = ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'];
        return `${nomes[Math.floor(Math.random() * nomes.length)]} ${sobrenomes[Math.floor(Math.random() * sobrenomes.length)]}`;
    }

    gerarCPF() {
        return Array.from({ length: 11 }, () => Math.floor(Math.random() * 10)).join('');
    }

    gerarRG() {
        return Array.from({ length: 9 }, () => Math.floor(Math.random() * 10)).join('');
    }

    gerarTelefone() {
        return `(11) 9${Math.floor(100000000 + Math.random() * 899999999)}`;
    }

    gerarCEP() {
        return `${Math.floor(10000000 + Math.random() * 89999999)}`;
    }

    preencherDadosBasicosCliente() {
        const nome = this.gerarNomeAleatorio();
        this.nomeCadastrado = nome;
        const cpf = this.gerarCPF();
        const rg = this.gerarRG();
        const telefone = this.gerarTelefone();
        const email = `${nome.toLowerCase().replace(' ', '.')}@email.com`;
        const endereco = `Rua ${nome.split(' ')[1]}, ${Math.floor(1 + Math.random() * 999)}`;
        const cep = this.gerarCEP();

        cy.get(CadastroClientePage.ADICIONAR_CLIENTE).click();
        cy.get(CadastroClientePage.CPF).type(cpf);

        cy.contains('Consultar').should('be.visible').click();
        cy.contains('button', 'Adicionar novo').click();

        cy.get(CadastroClientePage.NOME).type(nome);
        cy.get(CadastroClientePage.RG).type(rg);

        cy.get(CadastroClientePage.SEXO).click();

        cy.contains('Feminino')
            .should('be.visible')
            .click();

        cy.get(CadastroClientePage.TELEFONE).type(telefone);
        cy.get(CadastroClientePage.EMAIL).type(email);

        this.preencherProfissao();
        this.preencherEndereco(endereco, cep);

        cy.get(CadastroClientePage.PROSSEGUIR).click();

        cy.get(CadastroClientePage.CONSULTOR).click();
        cy.contains('PACTO - MÉTODO DE GESTÃO').click();

        cy.contains('MUSCULAÇÃO').click();
        cy.contains('OUTROS').click();
        cy.contains('REABILITAÇÃO').click();
        cy.contains('MANHÃ').click();

        cy.intercept('POST', '**/cadastro-cliente/gravar').as('cadastroCliente');
    }

    cadastrarClienteVisitante() {
        this.preencherDadosBasicosCliente();

        cy.get(CadastroClientePage.SALVAR_VISITANTE).click();

        cy.wait('@cadastroCliente').its('response.statusCode').should('eq', 200);

        cy.contains(this.nomeCadastrado, { timeout: 15000 }).should('be.visible');
    }

    cadastrarClienteNegociacao() {
        this.preencherDadosBasicosCliente();

        cy.get(CadastroClientePage.REALIZAR_NEGOCIACAO).click();
        cy.wait('@cadastroCliente').its('response.statusCode').should('eq', 200);

        cy.intercept('POST', '**/negociacao/simular').as('simulacaoNegociacao')
        cy.wait('@simulacaoNegociacao')

        cy.get(CadastroClientePage.ENVIAR_LINK).scrollIntoView()
        cy.get(CadastroClientePage.ENVIAR_LINK).should('be.visible')
        cy.get(CadastroClientePage.ENVIAR_LINK).click()


        cy.intercept('POST', '**/negociacao/gravar').as('gravarContrato');
        cy.intercept('POST', '**/negociacao/simular').as('simularContrato');
        cy.wait('@gravarContrato').its('response.statusCode').should('eq', 200)
    }

    preencherProfissao() {
        const profissoes = ['Engenheiro', 'Médico', 'Professor', 'Advogado', 'Designer'];
        cy.get(CadastroClientePage.PROFISSAO).click();
        cy.get('input[data-cy^="input-component-"][data-cy$="-input"]')
            .click()
            .clear()
            .type(profissoes[Math.floor(Math.random() * profissoes.length)]);
        cy.contains('Salvar').click();
    }

    preencherEndereco(endereco, cep) {
        cy.intercept('GET', '**/teste/cep/*').as('consultaCep');
        cy.get(CadastroClientePage.CEP).type(cep);
        cy.get(CadastroClientePage.CONSULTAR_CEP).click();
        cy.wait('@consultaCep');
        cy.get(CadastroClientePage.ENDERECO).type(endereco);
    }

    transferirAluno(cpfAluno: string, empresa: string) {
        cy.get(CadastroClientePage.CPF).type(cpfAluno)
        cy.get(CadastroClientePage.BOTAO_CONSULTAR_CLIENTE).click()
        cy.get(CadastroClientePage.CLIENTE).click()
        cy.get(CadastroClientePage.TRANSFERIR_CLIENTE).click()
        cy.wait(5000)
        cy.get('#pch-tltp-empresa', { timeout: 60000 }).contains('Evolve - Und: 5')
    }

    validarAlunoExistente(nomeAluno: string) {
        cy.get(CadastroClientePage.ADICIONAR_CLIENTE).click();
        cy.get(CadastroClientePage.CAMPO_NOME_BUSCA).type(nomeAluno);
        cy.contains('button', 'Consultar').click();
        cy.contains(new RegExp(nomeAluno, 'i')).should('be.visible');
    }
}

export default new CadastrarClientePage();
