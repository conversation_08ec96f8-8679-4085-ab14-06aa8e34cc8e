import { GrauDeInstrucaoPage } from '../../../locators';
const { faker } = require('@faker-js/faker');

class GrauDeInstrucaoPage {

    elements = {

        ADICIONAR: '#btn-add-item',
        NOME: '[title="Doutorado"]',
        BUSCA: '#input-busca-rapida',
        LISTA: '#element-0 > :nth-child(2)',
        EXCLUIR: '#element-0-action-delete\\ \\(key\\)'

    }
    adicionarGrauInstrucao() {
        const nomeGRAU = faker.name.jobTitle(); 

        cy.get(GrauDeInstrucaoPage.ADICIONAR).click();
        cy.get(GrauDeInstrucaoPage.NOME).type(nomeGRAU);

        cy.contains('Salvar').should('be.visible').click();
        cy.contains('Grau de instrução cadastrado com sucesso!').should('be.visible');

        return cy.wrap(nomeGRAU); 
    }

    editarGrauInstrucao(nomeGRAU) {
        cy.intercept('GET', '**/grau-instrucao?filters=*').as('buscarGrau');

        cy.get(GrauDeInstrucaoPage.BUSCA).type(nomeGRAU);
        cy.wait(500);
        cy.wait('@buscarGrau');

        cy.get(GrauDeInstrucaoPage.LISTA).should('be.visible').click();

        const novoNomeGRAU = faker.name.jobTitle(); 
        cy.get(GrauDeInstrucaoPage.NOME).clear().type(novoNomeGRAU);

        cy.contains('Salvar').should('be.visible').click();
        cy.contains('Grau de instrução cadastrado com sucesso!').should('be.visible');

        return cy.wrap(novoNomeGRAU); 
    }


    excluirGrauInstrucao(nomeGRAU) {
        cy.intercept('GET', '**/grau-instrucao?filters=*').as('buscarGrau');

        cy.get(GrauDeInstrucaoPage.BUSCA).clear().type(nomeGRAU);
        cy.wait(500);
        cy.wait('@buscarGrau');

        cy.contains(nomeGRAU, { timeout: 10000 }).should('be.visible')
        cy.get(GrauDeInstrucaoPage.EXCLUIR, { timeout: 15000 }).should('be.visible').click();

        cy.contains('Grau de instrução excluído com sucesso.').should('be.visible');
    }
}


export default new GrauDeInstrucaoPage();