import { PessoasPage   } from '../../locators';
import { PessoasPage } from '../../../locators';
class PessoasPageClass {

    elements = {

        PESSOAS: '#taskbar-resource-pessoas > .pct',
        EMPRESA: '#element-0 > :nth-child(4) > .column-cell > .empresaCell > :nth-child(1)'


    }

    validarNomeCurto(): void {
        cy.wait(500)
        cy.get(PessoasPage.PESSOAS).click()
        cy.get(PessoasPage.EMPRESA)
        .invoke('text')
        .then((text) => {
          expect(text.trim().toLowerCase()).to.equal('teste auto');
        });
      

    }
}

export default new PessoasPageClass();