import { SorteioPage   } from '../../locators';
import { SorteioPage } from '../../../locators';
class SorteioPageClass {
    elements = {

        BOTAO_TODOS: '.check-selecionar-todos > pacto-cat-checkbox > .cat-input-wrapper > label > .checkmark-outline',
        BOTAO_CONFIG_REALIZARSORTEIO: '[class="ng-star-inserted"]',
        BOTAO_SALVAR_VOLTAR: '[class="row justify-content-end custom-buttons"]',
        BOTAO_VOLTAR_REVELAR: '[class="row justify-content-center custom-btns"]',
        BOTAO_VOLTAR_VALIDAR: '[class="row custom-btns"]'

    }

    configurarSorteio(): void {
        cy.wait(300)
        cy.get(SorteioPage.BOTAO_CONFIG_REALIZARSORTEIO).find('span').contains('Configurações').click();
        cy.get(SorteioPage.BOTAO_TODOS, {timeout: 50000}).should('be.visible').click();
        cy.get(SorteioPage.BOTAO_SALVAR_VOLTAR).find('span').contains('Salvar').click();
        cy.contains('Últimos sorteios', {timeout: 50000}).should('be.visible')

    }

    realizarSorteio(): void {
        cy.wait(300)
        cy.get(SorteioPage.BOTAO_CONFIG_REALIZARSORTEIO).find('span').contains('Realizar sorteio').click();
        cy.contains('Sorteio está realizado!', {timeout: 50000}).should('be.visible')
        cy.get(SorteioPage.BOTAO_VOLTAR_REVELAR).find('span').contains('Revelar ganhador').click();
        cy.contains('Validar resultado', {timeout: 50000}).should('be.visible')
        cy.get(SorteioPage.BOTAO_VOLTAR_VALIDAR).find('span').contains('Validar resultado').click();
    }

}

export default new SorteioPageClass();
