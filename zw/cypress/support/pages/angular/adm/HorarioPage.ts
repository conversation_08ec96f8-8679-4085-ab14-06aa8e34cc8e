import * as faker from 'faker-br'
import { HorarioPage } from '../../../locators';

class HorarioPageClass {
    elements = {
        ADICIONAR: '#btn-add-item',
        DESCRICAO: '#horario-input-descricao',
        CHECK_LIVRE: 'pacto-cat-checkbox #horario-chk-livre-input',
        SALVAR: '#horario-btn-salvar',
        BUSCA_RAPIDA: '#input-busca-rapida',
        EDITAR_HORARIO: '[id^="element-"][id$="-edithorario"]',
        EXCLUIR_HORARIO: '[id^="element-"][id$="-deletehorario"]',
        EXCLUIR_MODAL: '#btn-action-modal > .content',
        DOMINGO: '#element-0-edithorariomod',
        SEGUNDA: '#element-1-edithorariomod',
        TERCA: '#element-2-edithorariomod',
        QUARTA: '#element-3-edithorariomod',
        QUINTA: '#element-4-edithorariomod',
        SEXTA: '#element-5-edithorariomod',
        SABADO: '#element-6-edithorariomod',
        HORA_INICIAL: 'pacto-cat-select-filter > #add-horario-select-hora-inicial',
        HORA_FINAL: 'pacto-cat-select-filter > #add-horario-select-hora-final',
        ADICIONAR_HORARIO: '#add-horario-btn-add-hora > span',
        SALVAR_HORARIO: '#add-horario-btn-salvar > .content',
        HORARIO_DOMINGO: '#element-0 > :nth-child(2) > .column-cell',
        HORARIO_SEGUNDA: '#element-1 > :nth-child(2) > .column-cell',
        HORARIO_QUARTA: '#element-3 > :nth-child(2) > .column-cell',
        EDITAR_HORARIO_DOMINGO: '#element-0-edithorariomod',
        SCROLL_CONTENT: '.scroll-content',
    }

    criarHorarioLivre(): string {
        const nomeHorario = `HORÁRIO LIVRE ${faker.random.number()}`
        cy.get(HorarioPage.ADICIONAR).click()
        cy.get(HorarioPage.DESCRICAO).type(nomeHorario)
        cy.get(HorarioPage.CHECK_LIVRE).check({force: true})
        cy.get(HorarioPage.SALVAR).click()
        cy.contains('Horário cadastrado com sucesso!').should('be.visible')
        return nomeHorario
    }

    editarHorario(nomeHorario: string): void {
        cy.get(HorarioPage.BUSCA_RAPIDA).type(nomeHorario).wait(1000)
        cy.get(HorarioPage.EDITAR_HORARIO).click()
        cy.get(HorarioPage.DESCRICAO).type(' EDITADO')
        cy.get(HorarioPage.SALVAR).click()
        cy.contains('Horário cadastrado com sucesso!').should('be.visible')
    }

    excluirHorario(): void {
        cy.get(HorarioPage.EXCLUIR_HORARIO).click()
        cy.get(HorarioPage.EXCLUIR_MODAL).click()
        cy.contains('Horário excluído com sucesso.').should('be.visible')
        cy.get(HorarioPage.BUSCA_RAPIDA).clear()
    }

    criarHorarioEspecifico(): string {
        const nomeHorario = `HORÁRIO ESPECIFICO ${faker.random.number()}`
        cy.get(HorarioPage.ADICIONAR).click()
        cy.get(HorarioPage.DESCRICAO).type(nomeHorario)
        cy.get(HorarioPage.SEGUNDA).click()
        cy.get(HorarioPage.HORA_INICIAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).contains('08:00').should('exist').click({force: true})
        cy.get(HorarioPage.HORA_FINAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).last().contains('09:00').should('exist').click({force: true})
        cy.get(HorarioPage.ADICIONAR_HORARIO).click()
        cy.get(HorarioPage.SALVAR_HORARIO).click()

        cy.get(HorarioPage.QUARTA).click()
        cy.get(HorarioPage.HORA_INICIAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).contains('08:00').should('exist').click({force: true})
        cy.get(HorarioPage.HORA_FINAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).last().contains('09:00').should('exist').click({force: true})
        cy.get(HorarioPage.ADICIONAR_HORARIO).click()
        cy.get(HorarioPage.SALVAR_HORARIO).click()

        cy.get(HorarioPage.HORARIO_SEGUNDA).contains('08:00 - 09:00')
        cy.get(HorarioPage.HORARIO_QUARTA).contains('08:00 - 09:00')

        cy.get(HorarioPage.SALVAR).click()
        cy.contains('Horário cadastrado com sucesso!').should('be.visible')
        return nomeHorario
    }

    editarHorarioEspecifico(nomeHorario: string): void {
        cy.get(HorarioPage.BUSCA_RAPIDA).type(nomeHorario).wait(1000)
        cy.get(HorarioPage.EDITAR_HORARIO).click()
        cy.get(HorarioPage.DESCRICAO).type(' EDITADO')

        cy.get(HorarioPage.EDITAR_HORARIO_DOMINGO).click()
        cy.get(HorarioPage.HORA_INICIAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).contains('08:00').should('exist').click({force: true})
        cy.get(HorarioPage.HORA_FINAL).click()
        cy.get(HorarioPage.SCROLL_CONTENT).last().contains('09:00').should('exist').click({force: true})
        cy.get(HorarioPage.ADICIONAR_HORARIO).click()
        cy.get(HorarioPage.SALVAR_HORARIO).click()

        cy.get(HorarioPage.HORARIO_DOMINGO).contains('08:00 - 09:00')
        cy.get(HorarioPage.HORARIO_SEGUNDA).contains('08:00 - 09:00')
        cy.get(HorarioPage.HORARIO_QUARTA).contains('08:00 - 09:00')

        cy.get(HorarioPage.SALVAR).click()
        cy.contains('Horário cadastrado com sucesso!').should('be.visible')
    }

}

export default new HorarioPageClass();
