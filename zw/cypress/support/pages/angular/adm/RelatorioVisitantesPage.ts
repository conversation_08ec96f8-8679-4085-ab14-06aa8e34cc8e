import { RelatorioVisitantesPage } from '../../../locators';
class RelatorioVisitantePage {
    elements = {

        SITUACAO: '.aux-parent select',
        SELECIONAR_TODOS: '.check-selecionar-todos > pacto-cat-checkbox > .cat-input-wrapper > label > .checkmark-outline',
        TABELA_VISTANTE: '.table-content',
        PESQUISAR_CONSULTOR: '.input-pesquisa > .ng-valid',
        TABELA_CONSULTORES: '.filtros-consultores > :nth-child(1)',

    }

    selecionarSituacao(nomeSituacao): void {
        cy.wait(300)
        cy.contains('Relatório de visitantes', {timeout: 50000}).should('be.visible')
        cy.get(RelatorioVisitantesPage.SITUACAO).should('be.visible').first().select(nomeSituacao);

    }

    selecionarTodos(): void {
        cy.wait(300)
        cy.contains('Relatório de visitantes', {timeout: 50000}).should('be.visible')
        cy.get(RelatorioVisitantesPage.SELECIONAR_TODOS).should('be.visible').click({force: true})


    }

    validarVisitante(nomeVisitante): void {
        cy.wait(300)
        cy.contains('Relatório de visitantes', {timeout: 50000}).should('be.visible')
        cy.contains('span.lbl', 'Consultar').should('be.visible').click();
        cy.get(RelatorioVisitantesPage.TABELA_VISTANTE).should('be.visible').contains(nomeVisitante)

    }
}

export default new RelatorioVisitantePage();
