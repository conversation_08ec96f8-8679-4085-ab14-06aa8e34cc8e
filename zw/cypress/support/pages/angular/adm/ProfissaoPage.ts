import { ProfissaoPage } from '../../../locators';
class ProfissaoPageClass {

    elements = {
        ADICIONAR_PROFISSAO: '#btn-add-item',
        DESCRICAO: 'input[placeholder="Profissão"]',
        EDITAR: 'i.pct-edit',
        BUSCA: '#input-busca-rapida',
        EXCLUIR: '#element-0-action-delete\\ \\(key\\)',

    }

    adicionarProfissao(): void {
        cy.get(ProfissaoPage.ADICIONAR_PROFISSAO).click()
        cy.get(ProfissaoPage.DESCRICAO).click().type('QA');
        cy.contains('Salvar').click();
        cy.contains('Profissão cadastrada com sucesso! Profissão cadastrada com sucesso!').should('be.visible')

    }

    editarProfissao(): void {
        cy.wait(500)
        cy.get(ProfissaoPage.BUSCA, {timeout: 15000})
            .type('Qa')
            .should('exist')

        cy.wait(500)

        cy.get(ProfissaoPage.EDITAR, {timeout: 15000})
            .should('exist')
            .and('be.visible')
            .and('not.be.disabled')
            .click({multiple: true});

        cy.get(ProfissaoPage.DESCRICAO)
            .click()
            .clear()
            .type('QA 02');
        cy.contains('Salvar').click();
        cy.contains('Profissão cadastrada com sucesso! Profissão cadastrada com sucesso!').should('be.visible')
    }

    excluirProfissao(): void {

        cy.get(ProfissaoPage.BUSCA)
            .type('QA 02');

        cy.wait(1500);

        cy.get(ProfissaoPage.EXCLUIR, {timeout: 25000}).click();
        cy.contains('Profissão excluída com sucesso.')

        // Verifica que não existe mais nenhum elemento com a descrição QA
        cy.contains('QA 02').should('not.exist');
    }
}


export default new ProfissaoPageClass();
