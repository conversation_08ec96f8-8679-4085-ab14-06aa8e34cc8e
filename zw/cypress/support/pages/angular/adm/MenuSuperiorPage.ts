import {ModulosEnum} from "../modulos-enum";
import { MenuSuperiorPage } from '../../../locators';

class MenuSuperiorPageClass {

menuExplorar(nomeFuncionalidade: string, siglaModulo: ModulosEnum) {
        cy.get('#topbar-modules-toggle').click();
        cy.wait(1000); // aguardar animação
        if (siglaModulo) {
            let modulo = `#explorar-module-${siglaModulo}`;
            cy.get(modulo).should("be.visible");
            cy.get(modulo).realHover();
        }
        cy.wait(500); // aguardar animação

        cy.contains('.item-menu-name', nomeFuncionalidade, {timeout: 10000}).scrollIntoView();
        cy.contains('.item-menu-name', nomeFuncionalidade, {timeout: 10000}).should("be.visible");
        cy.contains('.item-menu-name', nomeFuncionalidade, {timeout: 10000})
            .find('a')
            .click({force: true});
    }

    buscarAluno(nomeAluno: string, aguardarUltimaRequisicao: boolean = false) {
        cy.intercept('GET', '**/TreinoWeb/prest/psec/alunos/*').as('telaAluno')
        cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato**').as('ultimaRequisicao');

        cy.get('#topbar-search-field').type(nomeAluno)
        cy.contains('[id^="cat-autocomplete-"]', nomeAluno).click()
        cy.wait('@telaAluno', {timeout: 20000})
        cy.contains('.nome', nomeAluno, {timeout: 20000}).should('be.visible')

        if (aguardarUltimaRequisicao) {
            cy.wait('@ultimaRequisicao', {timeout: 30000});
        }
    }

    buscarAlunoJSF(nomeAluno: string): void {
        cy.get('#topbar-search-field').type(nomeAluno)
        cy.contains('[id^="cat-autocomplete-"]', nomeAluno).click()
    }

    buscarFuncionalidadesNaLupa(funcionalidade: string, options: { matchMode?: 'exact' | 'contains' } = {}) {
        const matchMode = options.matchMode || 'contains';

        cy.intercept('GET', '**/v1/cliente?pesquisa=**').as('pesquisa');
        cy.get('#topbar-search-field')
            .should('exist') // Verifica se o campo está presente
            .should('be.visible') // Certifica que o campo está visível

        cy.get('#topbar-search-field').type(funcionalidade)
        cy.wait('@pesquisa', {timeout: 10000})

        cy.get('[id^=cat-autocomplete-]')
            .should('be.visible')

        const baseSelector = cy.get('[id^=autocomplete-func]')
            .find('*')

        if (matchMode === 'exact') {
            baseSelector.filter((index, el) => el.textContent?.toLowerCase().trim() === funcionalidade.toLowerCase().trim())
                .first()
                .click({force: true});
        } else {
            baseSelector.filter((index, el) => el.textContent?.toLowerCase().includes(funcionalidade.toLowerCase().trim()))
                .first()
                .click({force: true});
        }
    }

    menuExplorarJSF(siglaModulo: string, nomeFuncionalidade: string) {
        cy.get("#explorar-trigger").click();
        cy.wait(1000); // aguardar animação
        if (siglaModulo) {
            cy.get(`.menu-modulo.modulo-${siglaModulo}`).should("be.visible");
            cy.get(`.menu-modulo.modulo-${siglaModulo}`).realHover();
        }
        cy.wait(500); // aguardar animação
        cy.get(`.menu-${nomeFuncionalidade}.menu-explorar-item.item-menu`, ).scrollIntoView();
        cy.get(`.menu-${nomeFuncionalidade}.menu-explorar-item.item-menu`).should("be.visible", );
        cy.get(`.menu-${nomeFuncionalidade}.menu-explorar-item.item-menu`).click({force: true, });
    }

    acessarModuloTreinoFromJSF() {
        cy.get('[data-cy="modulo-sigla-NTR"]').click()
    }
}


export default new MenuSuperiorPageClass();
