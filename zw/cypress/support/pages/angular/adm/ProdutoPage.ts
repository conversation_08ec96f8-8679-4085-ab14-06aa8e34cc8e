import { fa } from '@faker-js/faker';
import * as faker from 'faker-br';
import { ProdutoPage   } from '../../locators';
import { ProdutoPage } from '../../../locators';

class ProdutoPageClass {
    elements = {
        BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
        RESULTADO_BUSCA: '.odd > :nth-child(2)',
        PRE_PAGO: '#form\\:valorPacote',
        POS_PAGO: '#form\\:valorPacotePos',
        SALVAR: '#form\\:salvar',
        MENSAGEM: '#form\\:msgProduto',
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        TIPO_PRODUTO: '#form\\:tipoProduto',
        CATEGORIA: '#form\\:categoriaProduto',
        VALOR: '#form\\:valor',
        EMPRESA: '#form\\:empresaCfg',
        VALOR_EMPRESA: '#form\\:valorempresaCfg',
        ADD_EMPRESA: '#form\\:addempresaCfg'
    }

    cadastrarProdutoConfigValorEmpresa(): string {
        const nomeProduto = faker.commerce.productName();
        cy.intercept('POST', '**/ZillyonWeb/prest/plano/produto?situacao=AT').as('requisicaoProduto');
        cy.intercept('POST', '**/ZillyonWeb/faces/produtoForm.jsp').as('salvarProduto');

        cy.get(ProdutoPage.NOVO).click()
        cy.get(ProdutoPage.CATEGORIA).select('LOJA', { force: true })
        cy.get(ProdutoPage.DESCRICAO).type(nomeProduto)
        cy.get(ProdutoPage.TIPO_PRODUTO).select('Produto Estoque')
        cy.get(ProdutoPage.VALOR).type('1000')
        cy.get(ProdutoPage.EMPRESA).select('NOME FANTASIA DA ACADEMIA')
        cy.get(ProdutoPage.VALOR_EMPRESA).type('3000')

        cy.get(ProdutoPage.ADD_EMPRESA).should('be.visible').click();

        cy.wait(3000);

        cy.contains('Gravar').should('be.visible').click();

        cy.wait('@requisicaoProduto').its('response.statusCode').should('eq', 200);
        cy.wait('@salvarProduto').its('response.statusCode').should('eq', 200);

        cy.get(ProdutoPage.MENSAGEM)
            .should('be.visible')
            .and('have.text', 'Dados Gravados com Sucesso');

        return nomeProduto;
    }

}


export default new ProdutoPageClass();