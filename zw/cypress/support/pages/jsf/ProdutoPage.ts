import * as faker from 'faker-br'
import { ProdutoPage } from 'locators';

class ProdutoPageClass {

    elements = {
        BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
        RESULTADO_BUSCA: '.odd > :nth-child(2)',
        PRE_PAGO: '#form\\:valorPacote',
        POS_PAGO: '#form\\:valorPacotePos',
        SALVAR: '#form\\:salvar',
        MENSAGEM: '#form\\:msgProduto',
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        TIPO_PRODUTO: '#form\\:tipoProduto',
        CATEGORIA: '#form\\:categoriaProduto',
        VALOR: '#form\\:valor',
        ADD_PACOTE: '#form\\:addPacote',
    }

    cadastrarProdutoCreditoPersonal(): void {
        const nomeProdutoEsperado = 'Crédito Personal Interno';

        cy.get(ProdutoPage.NOVO, {timeout: 10000}).click()
        cy.get(ProdutoPage.DESCRICAO).type(nomeProdutoEsperado)
        cy.get(ProdutoPage.CATEGORIA).select('5', {force: true})
        cy.get(ProdutoPage.TIPO_PRODUTO).select('Crédito Personal')
        cy.get(ProdutoPage.VALOR).type('1000')
        cy.get(ProdutoPage.PRE_PAGO).type('150,00')
        cy.get(ProdutoPage.POS_PAGO).type('200,00')

        cy.get(ProdutoPage.ADD_PACOTE).should('be.visible').click();

        cy.wait(3000);

        cy.get(ProdutoPage.PRE_PAGO).type('70,00')
        cy.get(ProdutoPage.POS_PAGO).type('80,00')
        cy.get(ProdutoPage.ADD_PACOTE).should('be.visible').click();

        cy.wait(3000);

        cy.contains('Gravar').should('be.visible').click();

        cy.get(ProdutoPage.MENSAGEM)
            .should('be.visible')
            .and('have.text', 'Dados Gravados com Sucesso');
    }

    cadastrarProdutoLocacao(valor: string): Cypress.Chainable {
        const nomeProduto = faker.name.findName()
        cy.get(ProdutoPage.NOVO).click()
        cy.get(ProdutoPage.DESCRICAO).type(nomeProduto)
        cy.get(ProdutoPage.TIPO_PRODUTO).select('Locação').wait(500)
        cy.get(ProdutoPage.CATEGORIA).select('SERVIÇOS')
        cy.get(ProdutoPage.VALOR).type(valor)
        cy.get(ProdutoPage.SALVAR).click()
        return cy.wrap(nomeProduto)
    }
}

export default new ProdutoPageClass();
