import { BICRMPage } from '../../locators';

class BICRMPageClass {
    elements = {
        METAS_VENDAS: '#form\\:tituloBICRM',
        METAS_FIDELIZACAO: '#form\\:biFidelizacao > .bi-crm-box-header',
        RESULTADO: '#form\\:biResultado > .bi-crm-box-header',
        STUDIO: '#form\\:biStudio > .bi-crm-box-header',
        OBEJECAO: '#form\\:biObjecoes > .bi-crm-box-header',
        DESEMPENHO_COLABORADORES: '#form\\:biDesempenho > .bi-crm-box-header',
        TITULO_LIGACAO_PENDENTE: '#form\\:agendamentoLigacaoPendente > .bi-crm-box-header-indicadores',
        TITULO_INDICACAO_SEM_CONTATO: '#form\\:indIndicacoes > .bi-crm-box-header-indicadores',
        TITULO_RECEPTIVO: '#form\\:indReceptivo > .bi-crm-box-header-indicadores',
        TITULO_OBJECAO: '#form\\:indObjecoes > .bi-crm-box-header-indicadores',
        LISTA_LIGACAO_PENDENTE : '#form\\:abrirListaAgendamentoLigacaoPendente',
        FECHAR_LISTA: '#hidemodalListaClientesBI',
        LISTA_INDICACAO_SEM_CONTATO: '#form\\:abrirListaIndicacoesSemContato',
        LISTA_RECEPTIVO: '#form\\:abrirListaContatoReceptivo',
        LISTA_OBJECAO: '#form\\:abrirListaObjecoes',
        FECHAR_MODAL_OBJECAO: '#hidemodalListaObjecoesClientes'
    }

    validarCarregamentoTela(): void {
        cy.wait(3000)
        cy.get(BICRMPage.METAS_VENDAS, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.METAS_FIDELIZACAO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.RESULTADO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.STUDIO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.OBEJECAO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.TITULO_LIGACAO_PENDENTE, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.TITULO_INDICACAO_SEM_CONTATO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.TITULO_RECEPTIVO, {timeout: 20000}).should('be.visible')
        cy.get(BICRMPage.TITULO_OBJECAO, {timeout: 20000}).should('be.visible')
        
        cy.get(BICRMPage.LISTA_LIGACAO_PENDENTE, {timeout: 20000}).should('be.visible').click()
        cy.contains('Lista da Meta')
        cy.get(BICRMPage.FECHAR_LISTA, {timeout: 20000}).should('be.visible').click()

        cy.get(BICRMPage.LISTA_INDICACAO_SEM_CONTATO, {timeout: 20000}).should('be.visible').click()
        cy.contains('Lista da Meta')
        cy.get(BICRMPage.FECHAR_LISTA, {timeout: 20000}).should('be.visible').click()

        cy.get(BICRMPage.LISTA_RECEPTIVO, {timeout: 20000}).should('be.visible').click()
        cy.contains('Lista da Meta')
        cy.get(BICRMPage.FECHAR_LISTA, {timeout: 20000}).should('be.visible').click()
    
        cy.get(BICRMPage.LISTA_OBJECAO, {timeout: 20000}).should('be.visible').click()
        cy.contains('Lista de Clientes Com Objeção Definitiva')
        cy.get(BICRMPage.FECHAR_MODAL_OBJECAO, {timeout: 20000}).should('be.visible').click()
    
    }


}


export default new BICRMPageClass();
