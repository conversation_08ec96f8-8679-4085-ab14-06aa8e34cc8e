import * as faker from 'faker-br'
import { UsuarioPage } from 'locators';

class UsuarioPageClass {
    elements = {
        CADASTRAR_NOVO: '#form\\:btnNovo',
        TIPO_USUARIO: '#form\\:tipoUsuario',
        NOME_COLABORADOR: '#form\\:nomecolaborador',
        DATA_NASCIMENTO: '#form\\:dataNascInputDate',
        CPF: '#form\\:cfp',
        EMAIL_COLABORADOR: '#form\\:emailColaborador',
        TIPO_COLABORADOR: '#form\\:tipoColaborador',
        ADD_TIPO_COLABORADOR: '#form\\:addTipoColaborador',
        USER_NAME: '#form\\:username',
        EMPRESA: '#form\\:empresa',
        COD_PERFIL_ACESSO: '#form\\:codPerfilAcesso',
        ADD_PERFIL_ACESSO: '#form\\:addPerfilAcesso',
        SALVAR: '#form\\:salvar',
        PIN: '.classDireita > table > tbody > tr > td > .form',
        SELECT_PERFIL_ACESSO_TREINO: '#form\\:codPerfilAcessoTreino',
        CONSULTAR_COLABORADOR: '#form\\:consultaDadosColaborador',
        INPUT_CONSULTAR_COLABORADOR: '#formColaborador\\:valorConsultaColaborador',
        CONSULTAR_COLABORADOR_MODAL: '#formColaborador\\:btnConsultarColaborador',
        USUARIO_ADRIANA: '.index-13 > :nth-child(2)',
    }

    cadastrarUsuarioComNovoColaborador(): string {
        const nomeColaborador = faker.name.findName()
        cy.get(UsuarioPage.CADASTRAR_NOVO).click()
        cy.get(UsuarioPage.TIPO_USUARIO).select('NC')
        cy.get(UsuarioPage.NOME_COLABORADOR, {timeout: 10000}).type('Novo colaborador ' + faker.random.number())
        cy.get(UsuarioPage.DATA_NASCIMENTO).type('10/10/2000').wait(800)
        cy.get(UsuarioPage.CPF, {timeout: 10000}).type(faker.br.cpf())
        cy.get(UsuarioPage.EMAIL_COLABORADOR).type(faker.internet.email())
        cy.get(UsuarioPage.USER_NAME).type('colaborador' + faker.random.number())
        cy.get(UsuarioPage.PIN).type('19283')
        cy.get(UsuarioPage.TIPO_COLABORADOR).select('AD')
        cy.get(UsuarioPage.ADD_TIPO_COLABORADOR).click().wait(800)
        cy.get(UsuarioPage.EMPRESA).select('1', {timeout: 10000})
        cy.get(UsuarioPage.COD_PERFIL_ACESSO, {timeout: 10000}).select('1')
        cy.get(UsuarioPage.ADD_PERFIL_ACESSO, {timeout: 10000}).click().wait(500)
        cy.get(UsuarioPage.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR').wait(500)
        cy.get(UsuarioPage.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
        return nomeColaborador
    }

    cadastrarUsuarioComColaboradorExistente(colaborador: string): void {
        cy.get(UsuarioPage.CADASTRAR_NOVO).click()
        cy.get(UsuarioPage.TIPO_USUARIO).select('CE')
        cy.get(UsuarioPage.CONSULTAR_COLABORADOR).click().wait(500)
        cy.get(UsuarioPage.INPUT_CONSULTAR_COLABORADOR).should('be.visible').type(colaborador)
        cy.get(UsuarioPage.CONSULTAR_COLABORADOR_MODAL).click().wait(500)
        cy.contains('a', colaborador.toUpperCase()).click().wait(500)
        cy.get(UsuarioPage.CPF, {timeout: 10000}).type(faker.br.cpf())
        cy.get(UsuarioPage.EMAIL_COLABORADOR).type(faker.internet.email())
        cy.get(UsuarioPage.USER_NAME).type('colaborador' + faker.random.number())
        cy.get(UsuarioPage.PIN).type('19283')
        cy.get(UsuarioPage.TIPO_COLABORADOR).select('AD')
        cy.get(UsuarioPage.ADD_TIPO_COLABORADOR).click().wait(500)
        cy.get(UsuarioPage.EMPRESA).select('1')
        cy.get(UsuarioPage.COD_PERFIL_ACESSO, {timeout: 10000}).select('1')
        cy.get(UsuarioPage.ADD_PERFIL_ACESSO, {timeout: 10000}).click().wait(500)
        cy.get(UsuarioPage.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR', {force: true}).wait(500)
        cy.get(UsuarioPage.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
    }

    editarUsuario(): void {
        cy.get(UsuarioPage.USUARIO_ADRIANA).click()
        cy.get(UsuarioPage.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR').wait(500)
        cy.get(UsuarioPage.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
    }

}


export default new UsuarioPageClass();
