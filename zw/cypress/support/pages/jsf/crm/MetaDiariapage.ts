import * as faker from 'faker-br'
import { MetaDiariaPage } from '../locators';

class MetaDiariaPage {

  elements = {
    RECEPTIVO: '#form\\:listaMetaCRM\\:4\\:dtglistaMetaCRM\\:1\\:tipoMetaStyleClass',
    NOME_PASSIVO: '#form\\:nomePassivo',
    CELULAR: '#form\\:telCelularPassivo',
    AGENDAR: '#form\\:apresentarAgendarPassivo',
    AULA_EXPERIMENTAL: '#form\\:agendamentoPassivo\\:0',
    SELECT_MODALIDADE: '#form\\:modalidadeAgendaPassivo',
    TIPO_PROFESSOR: '#form\\:selTipoColabRecep',
    INPUT_HORA: '#form\\:horaMinutoAgendamentoPassivo',
    SELECT_PROFESSOR: '#form\\:selColabRecep',
    CONCLUIR_PASSIVO: '#form\\:concluirAgendaPassivo',
    BUSCAR_AULAS: '#form\\:buscarAulasRecep',
    SELECT_AULAS: '#form\\:selAulaRecep',
    FECHAR_MODAL: '#formMdlMensagemGenerica\\:btnFecharModalGenerico'
  }

  agendarReceptivo(): string {
    const nomeReceptivo = faker.name.findName()
    cy.wait(1000)
    cy.get(MetaDiariaPage.RECEPTIVO).click()
    cy.get(MetaDiariaPage.NOME_PASSIVO).type(nomeReceptivo)
    cy.get(MetaDiariaPage.CELULAR).type('62981575978')
    cy.get(MetaDiariaPage.AGENDAR).click()
    cy.get(MetaDiariaPage.AULA_EXPERIMENTAL).click()
    cy.get(MetaDiariaPage.SELECT_MODALIDADE).select('FAZ TUDO')
    cy.get(MetaDiariaPage.TIPO_PROFESSOR).select('Professor (TreinoWeb)')
    cy.get(MetaDiariaPage.SELECT_PROFESSOR).select('PACTO - MÉTODO DE GESTÃO')
    cy.get(MetaDiariaPage.INPUT_HORA).type('21:00')
    cy.get(MetaDiariaPage.BUSCAR_AULAS).click()
    cy.get(MetaDiariaPage.SELECT_AULAS)
      .find('option')
      .contains('AULA TESTE TOTE')
      .then($option => {
        const value = $option.val()
        cy.get(MetaDiariaPage.SELECT_AULAS).select(value).first()
      })
    cy.get(MetaDiariaPage.CONCLUIR_PASSIVO).click()
    cy.contains('Agendamento do receptivo gravado com sucesso.')
    cy.get(MetaDiariaPage.FECHAR_MODAL).click()
    return nomeReceptivo
  }

}

export default new MetaDiariaPage()