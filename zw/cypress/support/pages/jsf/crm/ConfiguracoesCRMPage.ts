import { ConfiguracoesCRMPage } from '../../locators';
class ConfiguracoesCRMPageClass {

  elements = {
    DIRECIONAR_AGENDAMENTO: '#form\\:direcionaragendamentosexperimentaisagenda',
    GRAVAR: '#form\\:salvar',
    ABA_DADOS_PESSOAIS: '#form\\:dadosBasico_lbl',
    ABA_EMAIL: '#form\\:configuracaoEmail_lbl',
    REMETENTE_PADRAO: '#form\\:textColaboradorResponsavel',
    SELECIONAR_REMETENTE: '.richfaces_suggestionEntry > .rich-sb-cell-padding',
    REMOVER_REMENTENTE: '#form\\:removeRemetnete'
  }

  remetentePadrao(): void {
    cy.get(ConfiguracoesCRMPage.ABA_EMAIL).click()
    cy.get(ConfiguracoesCRMPage.REMOVER_REMENTENTE).click().wait(500)
    cy.get(ConfiguracoesCRMPage.REMETENTE_PADRAO).type('PACTO - MÉTODO DE GESTÃO')
    cy.get(ConfiguracoesCRMPage.SELECIONAR_REMETENTE).click()
    cy.get(ConfiguracoesCRMPage.GRAVAR).click()
    cy.contains('Operação Realizada com sucesso!')
  }

  direcionarAgendamentoParaAgenda(habilitar: boolean): void {
    cy.get(ConfiguracoesCRMPage.ABA_DADOS_PESSOAIS).click()
    if (habilitar) {
      cy.get(ConfiguracoesCRMPage.DIRECIONAR_AGENDAMENTO).check()
    } else {
      cy.get(ConfiguracoesCRMPage.DIRECIONAR_AGENDAMENTO).uncheck()
    }
    cy.get(ConfiguracoesCRMPage.GRAVAR).click()
    cy.contains('Operação Realizada com sucesso!')
  }


}

export default new ConfiguracoesCRMPage()