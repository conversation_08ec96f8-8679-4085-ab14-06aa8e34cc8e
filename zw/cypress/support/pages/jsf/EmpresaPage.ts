import { EmpresaPage } from '../../locators';
class EmpresaPageClass {

    elements = {
        //Pró-Rata
        CONFIG_EMPRESA: '#form\\:configuracaoEmpresa_lbl',
        NUMERO_DIAS_PRO_RATA: '#form\\:nrDiasProrata',
        TOLERANCIA_PRO_RATA: '#form\\:toleranciaprorata',
        GRAVAR_EMPRESA: '#form\\:salvar',
        PARCELA_PRO_RATA: 'adm-parcelas-negociacao.ng-star-inserted > table > :nth-child(1) > :nth-child(1)',

        ABA_COBRANCA: '#form\\:tabConfgCobranca_lbl',

        PERMITIR_GERAR_BOLETO: '#form\\:panelGridGeralConfigBoleto > :nth-child(2) > :nth-child(1) > .classDireita > .campos',
        BOLETO_PADRAO: '#form\\:comboConvenioBoletoPadrao',
        BOTAO_SALVAR: '#form\\:salvar',

        ABA_EMPRESA: '#form\\:configuracaoEmpresa_lbl',
        ARREDONDAR_PARCELA: '#form\\:Arredondar',
        MENSAGEM: '#form\\:mensagem',

        NOME_CURTO:'#form\\:nomeCurto'
    }


    ajustarProRataEmpresa(nrDiasProrata: string = '20', toleranciaProRata: string = '0'): void {
        cy.wait(5000)
        
        cy.contains('td', 'NOME FANTASIA DA ACADEMIA')
            .should('be.visible')
            .click();

        cy.get(EmpresaPage.CONFIG_EMPRESA).click()
        cy.get(EmpresaPage.NUMERO_DIAS_PRO_RATA)
            .clear()
            .type(nrDiasProrata);

        cy.get(EmpresaPage.TOLERANCIA_PRO_RATA)
            .clear()
            .type(toleranciaProRata);

        cy.get(EmpresaPage.GRAVAR_EMPRESA).click()

        cy.contains('Dados Gravados com Sucesso').should('be.visible')

    }

    permitirGerarBoleto(): void {
        cy.wait(3000)
        cy.contains('NOME FANTASIA DA ACADEMIA').click()
        cy.get(EmpresaPage.ABA_COBRANCA, {timeout: 10000}).should('be.visible').click()
        cy.get(EmpresaPage.PERMITIR_GERAR_BOLETO, {timeout: 10000}).should('be.visible').check()
        cy.get(EmpresaPage.BOLETO_PADRAO, {timeout: 10000}).should('be.visible').select('BOLETO')
        cy.get(EmpresaPage.BOTAO_SALVAR, {timeout: 10000}).should('be.visible').click()
        cy.wait(3000)

    }

    naoPermitirGerarBoleto(): void {
        cy.wait(3000)
        cy.contains('NOME FANTASIA DA ACADEMIA').click()
        cy.get(EmpresaPage.ABA_COBRANCA, {timeout: 10000}).should('be.visible').click()
        cy.get(EmpresaPage.PERMITIR_GERAR_BOLETO, {timeout: 10000}).should('be.visible').uncheck()
        cy.get(EmpresaPage.BOTAO_SALVAR, {timeout: 10000}).should('be.visible').click()
        cy.wait(3000)

    }


    marcaArredondarParcela(): void {
        cy.wait(3000)
        cy.contains('NOME FANTASIA DA ACADEMIA').click()
        cy.get(EmpresaPage.ABA_EMPRESA).should('be.visible').click();
        cy.get(EmpresaPage.ARREDONDAR_PARCELA).select("ARRENDONDAR_CENTAVOS").should("have.value", "ARRENDONDAR_CENTAVOS");
        cy.get(EmpresaPage.BOTAO_SALVAR).should('be.visible').click({force: true});
        cy.get(EmpresaPage.MENSAGEM).should('be.visible').should("have.text", "Dados Gravados com Sucesso");

    }

    desmarcaArredondarParcela(): void {
        cy.wait(3000)
        cy.contains('NOME FANTASIA DA ACADEMIA').click()
        cy.get(EmpresaPage.ABA_EMPRESA).should('be.visible').click();
        cy.get(EmpresaPage.ARREDONDAR_PARCELA).select("NAO_USA").should("have.value", "NAO_USA");
        cy.get(EmpresaPage.BOTAO_SALVAR).should('be.visible').click({force: true});
        cy.get(EmpresaPage.MENSAGEM).should('be.visible').should("have.text", "Dados Gravados com Sucesso");

    }

    adicionarNomeCurto(): void {
        cy.wait(2000)
        cy.contains('NOME FANTASIA DA ACADEMIA').click()
        cy.get(EmpresaPage.NOME_CURTO).clear().type('Teste Auto')
        cy.get(EmpresaPage.BOTAO_SALVAR).should('be.visible').click({force: true});
        cy.get(EmpresaPage.MENSAGEM).should('be.visible').should("have.text", "Dados Gravados com Sucesso");


}
}

export default new EmpresaPageClass();
