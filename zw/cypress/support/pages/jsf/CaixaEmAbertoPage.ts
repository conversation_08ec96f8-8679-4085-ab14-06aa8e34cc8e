import { CaixaEmAbertoPage } from '../../locators';
class CaixaEmAbertoPageClass {

	private static readonly TIMEOUT = 20000

	private static readonly elements = {
		DETALHE_PARCELAS: '.fa-icon-plus-sign',
		SELECIONA_TODAS_PARCELAS: '#form\\:tabelaItens\\:0\\:selecionarTodasParcelas',
		TABELA_PARCELAS: '#form\\:tabelaItens\\:0\\:tabelaParcelas\\:0\\:tabelaParcelaValor',
		BOTAO_GERAR_BOLETO: '#form\\:btnGerarBoletoCaixa',
		SELECIONAR_BOLETO: '#form\\:comboConvenioBoletoPadrao',
		BOTAO_GRAVAR_BOLETO: '#form\\:btnGravar',
		CLIENTE: '#form\\:valorConsulta',
		BOTAO_BUSCAR_PARCELAS: '#form\\:botaoBuscarParcelas',
		BOTAO_RECEBER: '#form\\:btnReceberCaixaAberto',
		BOTAO_RECEBER_ANGULAR: '#caixa-em-aberto-btn-receber',
		PRIMEIRA_PARCELA: '#form\\:tabelaItens\\:0\\:tabelaParcelas\\:0\\:selecionarParcela',
		LOADER: '.rich-mpnl-body > img',
		RENEGOCIAR_PARCELAS: '#form\\:tabelaItens\\:0\\:renegociarParcelas',
		INPUT_QTD_PARCELAS: '.classDireita > input',
		BOTAO_RENEGOCIAR: '#formRenegociar\\:linkRenegociar',
		RADIO_DESCONTO: '#form\\:desconto\\:0',
		RADIO_TAXA_JUROS: '#form\\:desconto\\:1',
		INPUT_VALOR_DESCONTO: '#form\\:taxa',
		INPUT_VALOR_TAXA_JUROS: ':nth-child(2) > .form',
		BOTAO_DESCONTO: '#form\\:adicionar',
		BOTAO_TAXA: 'input[value="Adicionar"]',
		TABELA_PARCELAS_RENEGOCIAR: 'form\\:tblParcelasParaRenegociar:tb',
		DESCRICAO_PARCELA: '#form\\:tblParcelasParaRenegociar',
		INPUT_PARCELA_RENEGOCIADA: 'form\\:tblParcelasRenegociadas\\:0\\:idITValorParcela',
		TABELA_PARCELAS_RENEGOCIADAS: '#form\\:tblParcelasRenegociadas\\:tb',
		CONFIRMAR_RENEGOCIACAO: '#form\\:btnConfirmar',
		CANCELAR_PARCELA: '#form\\:btnCancelarCaixaAbertos',
		JUSTIFICATIVA_CANCELAMENTO: '#frmJustificativa\\:txtJustificativa',
		CONFIRMAR_JUSTIFICATIVA: '#frmJustificativa\\:confirmarCancelamentoParc',
        INPUT_PIN: '#formSenhaAutorizacao\\:senha',
        AUTORIZAR_PIN: '#formSenhaAutorizacao\\:btnAutorizar'
	}

	validaValoresParcelas(parcela: string, valorTotal: string): void {
		cy.get(CaixaEmAbertoPage.DETALHE_PARCELAS, { timeout: CaixaEmAbertoPage.TIMEOUT }).click()
		cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS).should('be.visible').click({ force: true })
		cy.get(CaixaEmAbertoPage.TABELA_PARCELAS).contains(parcela)
		cy.contains('span', `Total R$ ${valorTotal}`).should('be.visible')
	}

	gerarBoleto(nomeAluno?: string): void {
		cy.wait(3000)
		cy.get(CaixaEmAbertoPage.CLIENTE, { timeout: CaixaEmAbertoPage.TIMEOUT }).clear().type(nomeAluno)
		cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click()
		cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS, { timeout: CaixaEmAbertoPage.TIMEOUT }).should('be.visible').click({ force: true })
		cy.get(CaixaEmAbertoPage.BOTAO_GERAR_BOLETO, { timeout: CaixaEmAbertoPage.TIMEOUT }).should('be.visible').click({ force: true })
		cy.get(CaixaEmAbertoPage.SELECIONAR_BOLETO, { timeout: CaixaEmAbertoPage.TIMEOUT }).should('be.visible').select('BOLETO')
		cy.get(CaixaEmAbertoPage.BOTAO_GRAVAR_BOLETO, { timeout: CaixaEmAbertoPage.TIMEOUT }).should('be.visible').click({ force: true })
		cy.wait(3000)
	}

	receberParcelaAluno(nomeAluno: string): void {
		cy.get(CaixaEmAbertoPage.CLIENTE, { timeout: CaixaEmAbertoPage.TIMEOUT }).clear().type(nomeAluno)
		cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click().wait(2000)
		cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS).click()
		cy.get(CaixaEmAbertoPage.BOTAO_RECEBER).click()
	}

	acaoRenegociarParcela(nomeAluno: string, nrParcelas: string): void {
		cy.get(CaixaEmAbertoPage.CLIENTE, { timeout: CaixaEmAbertoPage.TIMEOUT }).clear().type(nomeAluno)
		cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click().wait(1500)
		cy.get(CaixaEmAbertoPage.DETALHE_PARCELAS, { timeout: CaixaEmAbertoPage.TIMEOUT }).click()
		cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
		cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS)
			.should('be.visible')
			.click()
		cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
		cy.get(CaixaEmAbertoPage.RENEGOCIAR_PARCELAS)
			.should('be.visible')
			.click()
		cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
		cy.get(CaixaEmAbertoPage.INPUT_QTD_PARCELAS)
			.clear()
			.type(nrParcelas)
		cy.get(CaixaEmAbertoPage.BOTAO_RENEGOCIAR).click()
	}

	renegociarParcelaDesconto(nomeAluno: string, nrParcelas: string, valorDesconto: string, valorFinalParcela: string): void {
		this.acaoRenegociarParcela(nomeAluno, nrParcelas)
		cy.get(CaixaEmAbertoPage.RADIO_DESCONTO).click()
		cy.get(CaixaEmAbertoPage.INPUT_VALOR_DESCONTO)
			.clear()
			.type(valorDesconto)
		cy.get(CaixaEmAbertoPage.BOTAO_DESCONTO).click()
		cy.contains('td', 'DESCONTOS').should('be.visible')
		cy.contains('td', valorDesconto).should('be.visible')
		cy.get(CaixaEmAbertoPage.TABELA_PARCELAS_RENEGOCIADAS).each(($row) => {
			cy.wrap($row)
				.find('input[type="text"]').last()
				.should('have.attr', 'value', valorFinalParcela);
		});
		cy.get(CaixaEmAbertoPage.CONFIRMAR_RENEGOCIACAO).click()
		cy.contains('Dados Gravados com Sucesso').should('be.visible')
	}

	renegociarParcelaTaxaJuros(nomeAluno: string, nrParcelas: string, valorTaxa: string, valorFinalParcela: string): void {
		this.acaoRenegociarParcela(nomeAluno, nrParcelas)
		cy.get(CaixaEmAbertoPage.RADIO_TAXA_JUROS).click()
		cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
		cy.get(CaixaEmAbertoPage.INPUT_VALOR_TAXA_JUROS)
			.clear()
			.type(valorTaxa)
		cy.get(CaixaEmAbertoPage.BOTAO_TAXA).click()
		cy.contains('td', 'TAXA/JUROS').should('be.visible')
		cy.contains('td', valorTaxa).should('be.visible')
		cy.get(CaixaEmAbertoPage.TABELA_PARCELAS_RENEGOCIADAS).each(($row) => {
			cy.wrap($row)
				.find('input[type="text"]').last()
				.should('have.attr', 'value', valorFinalParcela);
		});
		cy.get(CaixaEmAbertoPage.CONFIRMAR_RENEGOCIACAO).click()
		cy.contains('Dados Gravados com Sucesso').should('be.visible')
	}

    cancelarParcela(nomeAluno: string): void {
        cy.get(CaixaEmAbertoPage.CLIENTE, { timeout: CaixaEmAbertoPage.TIMEOUT }).type(nomeAluno)
        cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click()
        cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS, { timeout: CaixaEmAbertoPage.TIMEOUT }).click()
        cy.get(CaixaEmAbertoPage.CANCELAR_PARCELA).click()
        cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
        cy.get(CaixaEmAbertoPage.JUSTIFICATIVA_CANCELAMENTO).type('TESTE')
        cy.get(CaixaEmAbertoPage.CONFIRMAR_JUSTIFICATIVA).click()
        cy.get(CaixaEmAbertoPage.LOADER).should('not.be.visible')
        cy.get(CaixaEmAbertoPage.INPUT_PIN).type('123')
        cy.get(CaixaEmAbertoPage.AUTORIZAR_PIN).click()
        cy.contains('Parcelas Canceladas com Sucesso!').should('be.visible')
    }

    receberPrimeiraParcela(nomeAluno: string): void {
        cy.get(CaixaEmAbertoPage.CLIENTE, { timeout: 20000 }).type(nomeAluno)
        cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click()
        cy.get(CaixaEmAbertoPage.DETALHE_PARCELAS, { timeout: 20000 }).click()
        cy.get(CaixaEmAbertoPage.PRIMEIRA_PARCELA, { timeout: 20000 }).click()
        cy.get(CaixaEmAbertoPage.BOTAO_RECEBER).click()
    }

		selecionarParcelasCliente(nomeAluno: string): void {
			cy.get(CaixaEmAbertoPage.CLIENTE).clear().type(nomeAluno)
			cy.get(CaixaEmAbertoPage.BOTAO_BUSCAR_PARCELAS).click().wait(1000)
			cy.get(CaixaEmAbertoPage.SELECIONA_TODAS_PARCELAS).click()
		}

		getBotaoReceber(): void {
			cy.wait(1000)
			cy.get(CaixaEmAbertoPage.BOTAO_RECEBER).click()
		}

		getBotaoReceberAngular(): void {
			cy.wait(1000)
			cy.get(CaixaEmAbertoPage.BOTAO_RECEBER_ANGULAR).click()
		}

}

export default new CaixaEmAbertoPageClass();
