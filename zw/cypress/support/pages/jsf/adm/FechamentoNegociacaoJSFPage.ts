import DateUtils from "@utils/DateUtils"
import { FechamentoNegociacaoJSFPage } from '../../../locators';

class FechamentoNegociacaoJSFPageClass {
  private static readonly TIMEOUT = 10000

  validarProRata(dias: number, valor: string) {
    cy.get(FechamentoNegociacaoJSFPage.PRO_RATA, { timeout: FechamentoNegociacaoJSFPageClass.TIMEOUT }).select(DateUtils.futureDayOfMonth(dias))
    cy.get(FechamentoNegociacaoJSFPage.VALOR_PRIMEIRA_PARCELA).should('contain', valor)
  }

  concluirNegociacao(): void {
    cy.get(FechamentoNegociacaoJSFPage.CONCLUIR).click()
    cy.get(FechamentoNegociacaoJSFPage.LOADER).should('not.be.visible')
    cy.get(FechamentoNegociacaoJSFPage.INPUT_SENHA).type('123')
    cy.get(FechamentoNegociacaoJSFPage.CONFIRMAR_SENHA).click()
  }

}

export default new FechamentoNegociacaoJSFPage()