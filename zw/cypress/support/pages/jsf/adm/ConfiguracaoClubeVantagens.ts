class ConfiguracaoClubeVantagensClass {
  elements = {
    ABA_PLANOS: '#form\\:btnPlano',
    ABA_ACESSOS: '#form\\:btnAcesso',
    ABA_AULAS_COLETIVAS: '#form\\:btnAulasColetivas',
    ABA_PRODUTOS: '#form\\:btnProdutos',
    ABA_INDICACOES: '#form\\:btnIdicacoes',
    ABA_GERAL: '#form\\:btnGeral',
    INPUT_PLANO: '#form\\:nomePlano',
    INPUT_PONTUACAO: '#form\\:pontos',
    SALVAR_ABA_PLANO: '#form\\:salvarItemTipoPlano',
    INPUT_PONTOS_ACESSO: '#form\\:campanhaPontosAcesso',
    SALVAR_ABA_ACESSO: '#form\\:salvarItemTipoAcesso',
    INPUT_AULA: '#form\\:nomeAula',
    INPUT_PONTUACAO_AULA: '#form\\:pontosAula',
    SALVAR_ABA_AULAS: '#form\\:salvarItemTipoAula'
  }

  configurarPontoPorPlano(nomePlano: string, pontuacao: string): void {
    cy.get(ConfiguracaoClubeVantagensPage.ABA_PLANOS).click()
    cy.get(ConfiguracaoClubeVantagensPage.INPUT_PLANO).type(nomePlano, { delay: 0})
    cy.contains(nomePlano).click().wait(750)
    cy.get(ConfiguracaoClubeVantagensPage.INPUT_PONTUACAO).should('be.visible').clear().type(pontuacao).blur().wait(750)
    cy.get(ConfiguracaoClubeVantagensPage.SALVAR_ABA_PLANO).click({ force: true })
    cy.contains('Configurações gravadas com sucesso')
  }

  configurarPontoPorAcessos(pontuacao: string): void {
    cy.get(ConfiguracaoClubeVantagensPage.ABA_ACESSOS).click()
    cy.get(ConfiguracaoClubeVantagensPage.INPUT_PONTOS_ACESSO).clear().type(pontuacao)
    cy.get(ConfiguracaoClubeVantagensPage.SALVAR_ABA_ACESSO).click({ force: true })
    cy.contains('Configurações gravadas com sucesso')
  }

  configurarPontoPorAula(nomeAula: string, pontuacao: string): void {
    cy.get(ConfiguracaoClubeVantagensPage.ABA_AULAS_COLETIVAS).click()
    cy.get(ConfiguracaoClubeVantagensPage.INPUT_AULA).clear().type(nomeAula)
    cy.contains(nomeAula).click().wait(750)
    cy.get(ConfiguracaoClubeVantagensPage.INPUT_PONTUACAO_AULA).should('be.visible').clear().type(pontuacao).blur().wait(750)
    cy.get(ConfiguracaoClubeVantagensPage.SALVAR_ABA_AULAS).click({ force: true })
    cy.contains('Configurações gravadas com sucesso')
  }

}

export default new ConfiguracaoClubeVantagensClass();