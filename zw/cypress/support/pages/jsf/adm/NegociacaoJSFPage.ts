import DateUtils from "@utils/DateUtils"
import { NegociacaoJSFPage } from '../../../locators';

class NegociacaoJSFPageClass {
  private static readonly TIMEOUT = 10000

  montarNegociacao(nomePlano: string): void {
    cy.get(NegociacaoJSFPage.SELECT_PLANO).select(nomePlano)
    cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible')
    cy.get(NegociacaoJSFPage.DURACAO_MENSAL).should('not.be.visible').check({ force: true })
    cy.get(NegociacaoJSFPage.EM_12_VEZES).click({ force: true })
    cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible').wait(1000)
    cy.get(NegociacaoJSFPage.CONFERIR_NEGOCIACAO).should('be.visible').click()
  }


}

export default new NegociacaoJSFPage()