import { PerfilAcessoPage } from 'locators';
class PerfilAcessoPageClass {

    elements = {
        ADMINISTRADOR: '.index-1 > :nth-child(2)',
        PESQUISA_PERMISSAO: '#form\\:perfilAcesso',
        ZW_ADM: '#form\\:corpo\\:2\\:modulo_header',
        HABILITAR_PERMISSAO: '#form\\:corpo\\:2\\:acoes\\:103\\:permissao',
        GRAVAR: '#form\\:salvar',
        LUPA_PESQUISA: '#form\\:perfilAcesso',
        CLICAR_PERMISSAO: '#form\\:corpo\\:2\\:modulo_header',
        CHECK_PERMISSAO: 'tr.rich-table-row td input[type="checkbox"]',
        BOTAO_SALVAR: '#form\\:salvar',
        MENSAGEM: '#messageInfo',
        BOTAO_VOLTAR: '#form\\:consultar',
        EXPANDIR_SESSAO: '#form\\:containerPerfilAcesso',

    }

    habilitarPermissaoPerfilDeAcesso(): void {
        cy.get(PerfilAcessoPage.ADMINISTRADOR).click()
        cy.get(PerfilAcessoPage.PESQUISA_PERMISSAO).type('3.41')
        cy.get(PerfilAcessoPage.ZW_ADM).click()
        cy.get(PerfilAcessoPage.HABILITAR_PERMISSAO).should('be.visible').click()
        cy.get(PerfilAcessoPage.GRAVAR).click()
        cy.get('#messageInfo').should('be.visible')


    }


    addPermissao(permissao): void {
        ///Add o nome da permissão no arquivo spec, ex: '9.64 - Permite bloquear ou desbloquear cobrança automática do cliente'
        cy.wait(5000)
        cy.contains('ADMINISTRADOR').should('be.visible').click()
        cy.get(PerfilAcessoPage.LUPA_PESQUISA).should('be.visible').clear().type(permissao)
        cy.get(PerfilAcessoPage.EXPANDIR_SESSAO).should('be.visible').click()
        cy.wait(500)
        cy.contains(permissao) // Localiza o texto visível "permissao"
            .closest('tr') // Navega para a linha da tabela mais próxima do texto
            .find('input[type="checkbox"]') // Localiza o checkbox dentro dessa linha
            .should('exist') // Verifica se o checkbox existe
            .then(($checkbox) => {
                if (!$checkbox.is(':checked')) { // Se não estiver marcado
                    cy.wrap($checkbox).check({force: true}); // Marca o checkbox
                }
            });
        cy.get(PerfilAcessoPage.BOTAO_SALVAR).should('be.visible').click()
        cy.get(PerfilAcessoPage.MENSAGEM, {timeout: 50000}).should('be.visible').should('contain', 'Dados gravados com sucesso')
        cy.get(PerfilAcessoPage.BOTAO_VOLTAR).should('be.visible').click()

    }

    tirarPermissao(permissao): void {
        ///Add o nome da permissão no arquivo spec, ex: '9.64 - Permite bloquear ou desbloquear cobrança automática do cliente'

        cy.wait(5000)
        cy.contains('ADMINISTRADOR').should('be.visible').click()
        cy.get(PerfilAcessoPage.LUPA_PESQUISA).should('be.visible').clear().type(permissao)
        cy.get(PerfilAcessoPage.CLICAR_PERMISSAO).should('be.visible').click()
        cy.wait(500)
        cy.contains(permissao) // Localiza o texto visível "permissao"
            .closest('tr') // Navega para a linha da tabela mais próxima do texto
            .find('input[type="checkbox"]') // Localiza o checkbox dentro dessa linha
            .should('exist') // Verifica se o checkbox existe
            .then(($checkbox) => {
                if ($checkbox.is(':checked')) { // Se estiver marcado
                    cy.wrap($checkbox).uncheck({force: true}); // Deseleciona o checkbox
                }
            });
        cy.get(PerfilAcessoPage.BOTAO_SALVAR).should('be.visible').click()
        cy.get(PerfilAcessoPage.MENSAGEM, {timeout: 50000}).should('be.visible').should('contain', 'Dados gravados com sucesso')
        cy.get(PerfilAcessoPage.BOTAO_VOLTAR).should('be.visible').click()

    }


}


export default new PerfilAcessoPageClass();
