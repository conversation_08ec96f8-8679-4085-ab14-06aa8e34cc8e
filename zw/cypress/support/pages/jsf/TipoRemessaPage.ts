import { TipoRemessaPage } from 'locators';
class TipoRemessaPageClass {

    elements = {

        BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        TIPO_RETORNO: '#form\\:tipoRetorno',
        TIPO_REMESSA: '#form\\:tipoRemessa',
        ARQUIVO_REMESSA: '#form\\:arquivoLayoutRemessa',
        BOTAO_GRAVAR: '#form\\:salvar',
        MENSAGEM: '.mensagem',
        BOTAO_VOLTAR: '#form\\:consultar',
        BOTAO_EXCLUIR: '#form\\:excluir',
        BOTAO_EXCLUIR_SIM: '#formMdlMensagemGenerica\\:sim'


    }

    cadastrarTipoRemessa(descricaoRemessa): void {

        cy.get(TipoRemessaPage.BOTAO_CADASTRAR_NOVO, {timeout: 10000}).should('be.visible').click({force: true})
        cy.wait(300)
        cy.get(TipoRemessaPage.DESCRICAO, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa)
        cy.get(TipoRemessaPage.TIPO_RETORNO, {timeout: 10000}).should('be.visible').select('RETORNO PADRÃO DCC')
        cy.get(TipoRemessaPage.TIPO_REMESSA, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa)
        cy.get(TipoRemessaPage.ARQUIVO_REMESSA, {timeout: 10000}).should('be.visible').select('DCC/DCO')
        cy.get(TipoRemessaPage.BOTAO_GRAVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Gravados com Sucesso')
        cy.get(TipoRemessaPage.BOTAO_VOLTAR, {timeout: 10000}).should('be.visible').click({force: true})


    }

    editarTipoRemessa(descricaoRemessa): void {
        cy.wait(300)
        cy.contains(descricaoRemessa, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.DESCRICAO, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa + 'EDITADO')
        cy.get(TipoRemessaPage.BOTAO_GRAVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Gravados com Sucesso')
        cy.get(TipoRemessaPage.BOTAO_VOLTAR, {timeout: 10000}).should('be.visible').click({force: true})


    }

    excluirTipoRemessa(descricaoRemessa): void {
        cy.wait(300)
        cy.contains(descricaoRemessa + 'EDITADO', {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.BOTAO_EXCLUIR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.BOTAO_EXCLUIR_SIM, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(TipoRemessaPage.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Excluídos com Sucesso')


    }

}


export default new TipoRemessaPageClass();
