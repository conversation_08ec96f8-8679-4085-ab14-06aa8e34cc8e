import { VendasOnlineJSFPage } from '../../locators';
class VendasOnlinePageClass {

    elements = {
        PAINEL: '#form\\:vendasOnlinePanel',
        LOG: '#form\\:visualizarLogVendasOnlineConfig',
        BOTAO_VISUALIZAR_LOG: '#form\\:visualizarLogVendasOnlineConfig\\.botaoSecundario\\.texto-size-14-real\\.fa-icon-list',
        ABA_GESTAO: '#form\\:abaGestao',
        ICON_COGS: '.fa-icon-cogs',
        INTEGRACOES: '#form\\:integracoesVendasOnline',
        CHAVE_ANALYTICS: '#form\\:chaveAnalytics',
        PIXEL_FACEBOOK: '#form\\:pixelFacebook',
        API_CONVERSAO_FB: '#form\\:apiConversaoFacebook',
        GTM: '#form\\:googleTagManager',
        CHECK_BOT_CONVERSA: '#form\\:habIntBotConversa',
        ENDPOINT_API: '#form\\:endpointAPI',
        BTN_GRAVAR_CONFIG: '#form\\:gravarVendasConfig',
        MENSAGEM_SUCESSO: '#messageInfo'
    }

    validarAcessoTela(): void {
        cy.get(VendasOnlineJSFPage.PAINEL, { timeout: 3000 })
        .should('be.visible')
        .contains('Vendas não finalizadas')
    }

    addIntegracoes(): void {
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.ABA_GESTAO, { timeout: 3000 }).click({ force: true })
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.ICON_COGS, { timeout: 3000 }).click({ force: true })
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.INTEGRACOES, { timeout: 3000 }).click()
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.CHAVE_ANALYTICS, { timeout: 3000 }).clear().type('TESTE CHAVE')
        cy.get(VendasOnlineJSFPage.PIXEL_FACEBOOK, { timeout: 3000 }).clear().type('TESTE PIXEL')
        cy.get(VendasOnlineJSFPage.API_CONVERSAO_FB, { timeout: 3000 }).clear().type('TESTE FACEBOOK')
        cy.get(VendasOnlineJSFPage.GTM, { timeout: 3000 }).clear().type('TESTE GOOGLE')
        cy.get(VendasOnlineJSFPage.CHECK_BOT_CONVERSA, { timeout: 3000 }).check()
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.ENDPOINT_API, { timeout: 3000 }).clear().type('TESTE GOOGLE')
    }

    gravarEdicao(): void {
        cy.get(VendasOnlineJSFPage.BTN_GRAVAR_CONFIG, { timeout: 3000 }).click()
        cy.wait(3000)
        cy.get(VendasOnlineJSFPage.MENSAGEM_SUCESSO, { timeout: 3000 }).should('contain', 'Operação Realizada com sucesso!')
    }

    retirarIntegracoes(): void {
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.ABA_GESTAO, { timeout: 3000 }).click({ force: true })
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.ICON_COGS, { timeout: 3000 }).click({ force: true })
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.INTEGRACOES, { timeout: 3000 }).click()
        cy.wait(500)
        cy.get(VendasOnlineJSFPage.CHAVE_ANALYTICS, { timeout: 3000 }).clear()
        cy.get(VendasOnlineJSFPage.PIXEL_FACEBOOK, { timeout: 3000 }).clear()
        cy.get(VendasOnlineJSFPage.API_CONVERSAO_FB, { timeout: 3000 }).clear()
        cy.get(VendasOnlineJSFPage.GTM, { timeout: 3000 }).clear()
        cy.get(VendasOnlineJSFPage.CHECK_BOT_CONVERSA, { timeout: 3000 }).uncheck({ force: true })
        cy.wait(3000)
    }


}


export default new VendasOnlinePageClass();
