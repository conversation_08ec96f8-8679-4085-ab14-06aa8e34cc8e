import { RateioIntegracaoPage } from '../locators';
class RateioIntegracaoPageClass {

    elements = {
        TITULO: '.margin-box > .container-header-titulo',
        TIPO_RATEIO: '#formRateioEdicao\\:tipoRateio\\:0',
        PLANO: '#formRateioEdicao\\:btAddPlano',
        RECEITA: '#treeview\\:1\\:\\:descricaoDetalhada > .tituloCampos',
        VALOR: '#formRateioEdicao\\:valor',
        ADICIONAR_RATEIO: '#formRateioEdicao\\:botaoAdicionarRateio',
        SALVAR_RATEIO: '#formRateioEdicao\\:botaoSalvarRateios',
        MENSAGEM: '.mensagemDetalhada',
        EXCLUIR_RATEIO_0: '#formRateioEdicao\\:listaRateios\\:0\\:removerRateioEdicao',
        ADICIONAR_SERVICO: 'img[title="Adicionar Rateio"]'
    }

    validarAcesso(): void {
        cy.wait(5000)
        cy.get(RateioIntegracaoPage.TITULO, { timeout: 30000 })
          .should('be.visible')
          .should('contain', 'Rateio Integração')
    }

    cadastroRateioServico(): void {
        this.validarAcesso()

        cy.contains('td', 'SERVIÇO')
          .closest('tr')
          .find(RateioIntegracaoPage.ADICIONAR_SERVICO)
          .should('have.length', 1)
          .click()

        cy.get(RateioIntegracaoPage.TIPO_RATEIO, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(RateioIntegracaoPage.PLANO, { timeout: 30000 }).should('be.visible').click()
        cy.get(RateioIntegracaoPage.RECEITA, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(RateioIntegracaoPage.VALOR, { timeout: 30000 }).should('be.visible').clear().type('100,00')
        cy.get(RateioIntegracaoPage.ADICIONAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(RateioIntegracaoPage.SALVAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
    }

    validarMensagemRateioServico(): void {
        cy.wait(300)
        cy.get(RateioIntegracaoPage.MENSAGEM, { timeout: 30000 })
          .should('be.visible')
          .should('contain', 'O total da soma das percentagens do tipo Plano de Contas deve ser igual a 100%.')
    }

    excluirRateioServico(): void {
        cy.get(RateioIntegracaoPage.EXCLUIR_RATEIO_0, { timeout: 30000 }).should('be.visible').click()
        cy.wait(3000)
        cy.get(RateioIntegracaoPage.EXCLUIR_RATEIO_0, { timeout: 30000 }).should('be.visible').click()
        cy.get(RateioIntegracaoPage.SALVAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
    }
}

export default new RateioIntegracaoPageClass()
