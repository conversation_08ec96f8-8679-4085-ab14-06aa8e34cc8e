import { NovaContaAPagarPage } from '../locators';
class NovaContaAPagarPageClass {
    
    elements = {
        INPUT_PESSOA: '#formLanc\\:pessoa',
        SUGGESTION_PESSOA: '#formLanc\\:suggestionPessoa',
        INPUT_DESCRICAO: '#formLanc\\:descricao',
        INPUT_VALOR: '#formLanc\\:valor',
        SELECT_FORMA_PAGAMENTO: '#formLanc\\:formaPagamento',
        SELECT_CONTA: '#formLanc\\:contaSelectitem',
        BTN_SALVAR: '#formLanc\\:btnGravarLanc',
        MENSAGEM_SUCESSO: '#messageInfo',

        // Adicionados:
        INPUT_DATA_LANCAMENTO: '#formLanc\\:dtLancamentoInputDate',
        INPUT_DATA_VENCIMENTO: '#formLanc\\:dataVencimentoInputDate',
        ICONE_CALENDARIO_VENCIMENTO: '#formLanc\\:dataVencimentoPopupButton',
        CALENDARIO_VENCIMENTO: '#formLanc\\:dataVencimento',
        INPUT_DATA_COMPETENCIA: '#formLanc\\:dataCompetenciaInputDate',
        ICONE_CALENDARIO_COMPETENCIA: '#formLanc\\\:dataCompetenciaPopupButton',
        CALENDARIO_COMPETENCIA: '#formLanc\\:dataCompetencia',
        BTN_AGENDAR: '#formLanc\\:agendarPagamento',
        HEADER_AGENDAMENTO: '#agendamentoFinanceiroPanelHeader',
        RADIO_AGENDAMENTO: '#agendamentoFinanceiroForm\\:radioAgendamento\\:0',
        BTN_GRAVAR_AGENDAMENTO: '#agendamentoFinanceiroForm\\:gravarAgendamentoFin',
        PANEL_AGENDAMENTO: '#formLanc\\:panelAgendamentos_body',

        ///SENHA
        INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
        BTN_AUTORIZAR: '#formSenhaAutorizacao\\:btnAutorizar',

    }

    validarAcessoTela() {
        cy.wait(3000)
        cy.contains('Lançar Conta a Pagar')
    }

    preencherLancamento(pessoa, descricao, valor, formaPagamento, conta) {
        this.validarAcessoTela()
        cy.get(NovaContaAPagarPage.INPUT_PESSOA).should('be.visible').clear().type(pessoa).wait(1000)
        cy.contains(pessoa.toUpperCase()).click({force: true}).wait(600)
        cy.get(NovaContaAPagarPage.INPUT_DESCRICAO).should('be.visible').clear().type(descricao)
        cy.get(NovaContaAPagarPage.INPUT_VALOR).clear().type(valor).wait(300)
        cy.get(NovaContaAPagarPage.SELECT_FORMA_PAGAMENTO).should('be.visible').select(formaPagamento).wait(1000)
        cy.get(NovaContaAPagarPage.ICONE_CALENDARIO_VENCIMENTO).click().wait(1000)
        cy.get(NovaContaAPagarPage.CALENDARIO_VENCIMENTO).contains('1').click().wait(1000)
        cy.get(NovaContaAPagarPage.ICONE_CALENDARIO_COMPETENCIA).click().wait(1000)
        cy.get(NovaContaAPagarPage.CALENDARIO_COMPETENCIA).contains('1').click().wait(1000)
        cy.get(NovaContaAPagarPage.SELECT_CONTA).should('be.visible').select(conta)
    }

    agendarLancamento() {
        cy.get(NovaContaAPagarPage.BTN_AGENDAR, { timeout: 3000 }).should('be.visible').click()
        cy.get(NovaContaAPagarPage.HEADER_AGENDAMENTO, { timeout: 3000 }).should('be.visible')
        cy.get(NovaContaAPagarPage.RADIO_AGENDAMENTO, { timeout: 3000 }).should('be.visible').click()
        cy.get(NovaContaAPagarPage.BTN_GRAVAR_AGENDAMENTO).should('be.visible').click()
        cy.get(NovaContaAPagarPage.PANEL_AGENDAMENTO, { timeout: 3000 }).should('be.visible')
    }

    salvarLancamento() {
        cy.get(NovaContaAPagarPage.BTN_SALVAR).should('be.visible').click()
    }

    senhaLancamento() {
        cy.get(NovaContaAPagarPage.INPUT_SENHA, { timeout: 3000 }).should('be.visible').type('123')
        cy.get(NovaContaAPagarPage.BTN_AUTORIZAR).should('be.visible').click()
    }

    validarMensagemSucesso() {
        cy.get(NovaContaAPagarPage.MENSAGEM_SUCESSO).should('be.visible').and('contain', 'Dados Gravados com Sucesso')
    }
}

export default new NovaContaAPagarPageClass();
