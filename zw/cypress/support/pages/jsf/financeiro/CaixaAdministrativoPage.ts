import { CaixaAdministrativoPage } from '../../../locators';

class CaixaAdministrativoPageClass {
    validarAberturaPagina() {
        cy.get(CaixaAdministrativoPage.TITULO_PAGINA).contains('Operações realizadas no Caixa-Administrativo:')
        cy.get(CaixaAdministrativoPage.BOTAO_FECHAR_CAIXA).should('be.visible')
        cy.contains('Dados Consultados com Sucesso').should('be.visible')
    }
}


export default new CaixaAdministrativoPageClass();
