import DateUtils from "@utils/DateUtils";
import { ContaPagarPage } from '../../locators';

class ContaPagarPageClass {
  TIMEOUT = 10000
  
  elements = {
    PAGAR_PARA: '#formLanc\\:pessoa',
    ALUNO_SELECIONADO: '.richfaces_suggestionEntry > :nth-child(2)',
    GRAVAR: '#formLanc\\:btnGravarLanc',
    DESCRICAO: '#formLanc\\:descricao',
    INPUT_DATA_VENCIMENTO: '#formLanc\\:dataVencimentoInputDate',
    DATA_VENCIMENTO_CALENDARIO: '#formLanc\\:dataVencimentoPopupButton',
    DATA_COMPETENCIA_CALENDARIO: '#formLanc\\:dataCompetenciaPopupButton',
    DATA_HOJE: '#formLanc\\:dataVencimentoFooter > table > tbody > tr > :nth-child(5) > .rich-calendar-tool-btn',
    DATA_HOJE_COMPETENCIA: '#formLanc\\:dataCompetenciaFooter > table > tbody > tr > :nth-child(5) > .rich-calendar-tool-btn',
    LOADER: '.rich-mpnl-body > img',
    INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR: '#formSenhaAutorizacao\\:btnAutorizar',
    FECHAR_MODAL: '#hidelinkAutorizacao',
    VALOR: '#formLanc\\:valor'
  }

  acaoGravar(): void {
    cy.get(ContaPagarPage.GRAVAR).click().wait(1000)
    cy.get(ContaPagarPage.INPUT_SENHA).type('123')
    cy.get(ContaPagarPage.CONFIRMAR).click().wait(1000)
  }

  lancarContaPreenchendoCompetencia(favorecido: string): void {
    cy.get(ContaPagarPage.PAGAR_PARA).type(favorecido)
    cy.get(ContaPagarPage.ALUNO_SELECIONADO).click().wait(1000)
    cy.get(ContaPagarPage.DESCRICAO).type('CONTA DE TESTE', { delay: 0 })
    cy.get(ContaPagarPage.VALOR).focus().clear().type('20000').blur().wait(750)
    cy.get(ContaPagarPage.DATA_VENCIMENTO_CALENDARIO).click().wait(750)
    cy.get(ContaPagarPage.DATA_HOJE).click().wait(750)
    this.acaoGravar()
    cy.get(ContaPagarPage.FECHAR_MODAL).click().wait(1000)
    cy.contains('O campo DATA DE COMPETÊNCIA (Lançamento) deve ser informado.').should('be.visible')

    cy.get(ContaPagarPage.DATA_COMPETENCIA_CALENDARIO).click().wait(750)
    cy.get(ContaPagarPage.DATA_HOJE_COMPETENCIA).click()
    this.acaoGravar()
  }

  lancarContaSemPreencherCompetencia(favorecido: string): void {
    cy.get(ContaPagarPage.PAGAR_PARA).type(favorecido)
    cy.get(ContaPagarPage.ALUNO_SELECIONADO).click();
    cy.wait(1000);
    cy.get(ContaPagarPage.DESCRICAO).type('CONTA DE TESTE', {delay: 0})

    cy.get(ContaPagarPage.VALOR).clear();
    cy.get(ContaPagarPage.VALOR).type('20000', {delay: 0})
    cy.get(ContaPagarPage.VALOR).blur()
    cy.wait(750);

    cy.get(ContaPagarPage.INPUT_DATA_VENCIMENTO).clear()
    cy.get(ContaPagarPage.INPUT_DATA_VENCIMENTO).type(DateUtils.currenteDateBR())
    cy.get(ContaPagarPage.INPUT_DATA_VENCIMENTO).blur()
    cy.wait(750)

    this.acaoGravar()
    cy.contains('Dados Gravados com Sucesso').should('be.visible')
  }
}

export default new ContaPagarPage()
