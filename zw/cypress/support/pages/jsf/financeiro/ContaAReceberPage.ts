import { ContaAReceberPage } from '../../locators';
class ContaAReceberPageClass {
    elements = {
    CONSULTAR_PLANOS: '#formLanc\\:btAddPlano',
    EXPANDIR_RECEITA: '[id="treeview:1::tNodeP:handles"]',
    RELATORIO_PLANOS: '#treeview'
    }

    validarPlanoDeContas(nomePlano) {
     cy.get(ContaAReceberPage.CONSULTAR_PLANOS, {timeout:3000}).should('be.visible').click()
     cy.get(ContaAReceberPage.EXPANDIR_RECEITA ,{timeout:3000}).should('be.visible').click();
     cy.get(ContaAReceberPage.RELATORIO_PLANOS, {timeout:3000}).should('be.visible').contains(nomePlano)
    
    }

 

    
}

export default new ContaAReceberPageClass();
