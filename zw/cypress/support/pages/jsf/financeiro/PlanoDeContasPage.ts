import { PlanoDeContasPage } from '../locators';
class PlanoDeContasPageClass {
    elements = {
       BOTAO_NOVO: '#formBotoes\\:adicionar',
       DESCRICAO_PLANO: '#formEdicaoPlanoContas\\:descricao',
       BOTAO_CONSULTAR_PLANO: '#formEdicaoPlanoContas\\:btAddPlano',
       SELECIONAR_PLANO: '#treeview',
       BOTAO_GRAVAR_PLANO: '#formEdicaoPlanoContas\\:btnGravarModalPlanoContas',
       MENSAGEM: '.mensagem',
       BOTAO_FECHAR_MODAL: '#hidelink1',
       RELATORIO_PLANO_DE_CONTAS: '#form\\:dados_body',
       EXPANDIR_TUDO:'.expandirPlano',
       SELECT_TIPO: '#formEdicaoPlanoContas\\:tipoPadrao'
    }

    cadastrarPlanoDeContas() {
        function gerarNomePlano() {
            const prefixo = Math.random().toString(36).substring(2, 7).toUpperCase();
            return `${prefixo}PLANO`;
        }
    
        const nomePlano = gerarNomePlano();
        cy.get(PlanoDeContasPage.BOTAO_NOVO, { timeout: 3000 }).should('be.visible').click();
        cy.get(PlanoDeContasPage.BOTAO_CONSULTAR_PLANO, { timeout: 3000 }).should('be.visible').click();
    
        cy.get(PlanoDeContasPage.SELECIONAR_PLANO, { timeout: 3000 }).should('be.visible').contains('001 - Receitas (+)').click();
        cy.get(PlanoDeContasPage.DESCRICAO_PLANO, { timeout: 3000 }).should('be.visible').clear().type(nomePlano);
        cy.get(PlanoDeContasPage.SELECT_TIPO, { timeout: 3000 }).should('be.visible').select('Entrada')
        cy.get(PlanoDeContasPage.BOTAO_GRAVAR_PLANO, { timeout: 3000 }).should('be.visible').click();
        cy.get(PlanoDeContasPage.MENSAGEM, { timeout: 3000 }).should('be.visible').contains('Dados Gravados com Sucesso');
        this.fecharModalCadastro();
    
        return nomePlano; 
    }

    private fecharModalCadastro() {
        cy.get(PlanoDeContasPage.BOTAO_FECHAR_MODAL, {timeout:3000}).should('be.visible').click() 

    }

    validarNomePlano(nomePlano) {
        
        cy.get(PlanoDeContasPage.EXPANDIR_TUDO, {timeout:3000}).should('be.visible').click() 
        cy.get(PlanoDeContasPage.RELATORIO_PLANO_DE_CONTAS, {timeout:3000}).should('be.visible').contains(nomePlano)

    }

    editarNomePlano(nomePlano) {
        
        cy.get(PlanoDeContasPage.EXPANDIR_TUDO, {timeout:3000}).should('be.visible').click() 
        cy.get(PlanoDeContasPage.RELATORIO_PLANO_DE_CONTAS, {timeout:3000}).should('be.visible').contains(nomePlano)

    }

    
}

export default new PlanoDeContasPageClass();
