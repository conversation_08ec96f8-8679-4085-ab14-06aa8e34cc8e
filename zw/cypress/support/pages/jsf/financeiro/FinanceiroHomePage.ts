import { FinanceiroHomePage } from '../locators';
class FinanceiroHomePageClass {
    elements = {
        MODULO_FINANCEIRO: '',
        NOME_MODULO: '#zwUiModuloAtualDescricao',
        ABRIR_CAIXA: '.MENU-ABRIR_CAIXA',
        FECHAR_CAIXA: '.MENU_FECHAR_CAIXA',
        PIN: '#formModalLanc\\:senha',
        CHECKBOX_TODAS_CONTAS: '#formModalLanc\\:listaContas\\:selecionaTodasContas',
        GRAVAR: '#formModalLanc\\:gravar',
        CONFIRMAR_FECHAMENTO: '#formFechamentoCaixa\\:btnFechaCaixa',
        CONFIRMAR_MODAL: '#formGravarFecharCaixa\\:btnSimFecharCaixa',
        BOTAO_FECHAR_CAIXA: '#form\\:fecharCaixaBtn'
    }

    abrirCaixa() {
        cy.get(FinanceiroHomePage.NOME_MODULO, {timeout: 10000}).contains('Financeiro')
        cy.get('body').then(elemento => {
            const abrirCaixa = elemento.find(FinanceiroHomePage.ABRIR_CAIXA).length;
            if (abrirCaixa != 0) {
                cy.get(FinanceiroHomePage.ABRIR_CAIXA, {timeout: 10000}).last().click().wait(500)
                cy.get(FinanceiroHomePage.PIN).type('123')
                cy.get(FinanceiroHomePage.CHECKBOX_TODAS_CONTAS).click()
                cy.get(FinanceiroHomePage.GRAVAR).click()
                cy.contains('Abertura de caixa realizada com sucesso!').should('be.visible')
            }
        })
    }

    fecharCaixa() {
        cy.get(FinanceiroHomePage.NOME_MODULO, {timeout: 10000}).contains('Financeiro')
        cy.get('body').then(elemento => {
            const fecharCaixa = elemento.find(FinanceiroHomePage.FECHAR_CAIXA).length;
            if (fecharCaixa != 0) {
                cy.capturaPopUp()
                cy.get(FinanceiroHomePage.BOTAO_FECHAR_CAIXA, {timeout: 10000}).last().click().wait(500)
                cy.get(FinanceiroHomePage.CONFIRMAR_FECHAMENTO).click()
                cy.get(FinanceiroHomePage.CONFIRMAR_MODAL).click()
                cy.contains('Fechamento de caixa realizado com sucesso!').should('be.visible')
            }
        })
    }
}


export default new FinanceiroHomePageClass();
