import * as faker from 'faker-br';
import { TelaFormasDePagamentoPage } from '../../locators';

class TelaFormasDePagamentoClass {

cadastroFormaPagamentoPinpad(nomeFormaPagamento) {
    cy.wait(3000);
    cy.get(TelaFormasDePagamentoPage.BOTAO_CADASTRAR).click();
    cy.get(TelaFormasDePagamentoPage.DESCRICAO)
      .should("be.visible")
      .type(nomeFormaPagamento);
    cy.get(TelaFormasDePagamentoPage.TIPO_PAGAMENTO)
      .should("be.visible")
      .select("Cartão de Crédito");
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.SOMENTO_FINANCEIRO).uncheck({ force: true });
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.SELECIONAR_PINPAD).should("be.visible").select("Linx");
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.DESCRICAO_PINPAD_CAPPTA)
      .should("be.visible")
      .type(nomeFormaPagamento);
    cy.get(TelaFormasDePagamentoPage.SERIAL_CAPPTA).should("be.visible").type("010101");
    cy.get(TelaFormasDePagamentoPage.USAR_PINPAD).should("be.visible").check();
    cy.get(TelaFormasDePagamentoPage.ADD_PINPAD).should("be.visible").click();
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.BOTAO_SALVAR).click();
  }

  excluirFormaPagamentoPinPad(nomeFormaPagamento) {
    cy.wait(3000);
    cy.get(TelaFormasDePagamentoPage.SELECIONAR_QUANTIDADE).select("Todos");
    cy.contains(nomeFormaPagamento).click();
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.DESCRICAO).should("be.visible").clear();

    cy.get('input[type="button"][value="Remover"]').click();
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.BOTAO_CONFIRMAR_SIM).click();
    cy.get(TelaFormasDePagamentoPage.BOTAO_EXCLUIR).click();
    cy.wait(300);
    cy.get(TelaFormasDePagamentoPage.BOTAO_CONFIRMAR_SIM).click();
    cy.contains("Dados Excluídos com Sucesso");
  }
}

export default new TelaFormasDePagamentoClass();
