import * as faker from 'faker-br'
import { ColaboradorPage } from '../../locators';

class ColaboradorPageClass {
    elements = {
        CADASTRAR_NOVO: '#form\\:btnNovo',
        NOME_COLABORADOR: '#form\\:nome',
        DATA_NASCIMENTO: '#form\\:dataNascInputDate',
        SALVAR: '#form\\:salvar',
        DADOS_COLABORADOR: '#form\\:abaDadosColaborador_lbl',
        TIPO_COLABORADOR: '#form\\:tipoColaborador',
        ADICIONAR: '#form\\:addTipoColaborador'
    }

    cadastrarColaborador(): string {
        const nomeColaborador = 'Novo colaborador ' + faker.random.number()
        cy.get(ColaboradorPage.CADASTRAR_NOVO).click()
        cy.get(ColaboradorPage.NOME_COLABORADOR, { timeout: 10000 }).type(nomeColaborador)
        cy.get(ColaboradorPage.DATA_NASCIMENTO).type('10/10/2000')
        cy.get(ColaboradorPage.SALVAR, { timeout: 10000 }).should('be.visible').click({ force: true })
        cy.contains('Dados Gravados com Sucesso')
        return nomeColaborador
    }

    cadastrarPersonal(nomeColaborador?: string): string {
        const nomeGerado = nomeColaborador || 'Novo colaborador ' + faker.random.number();
    

        cy.intercept('POST', '**/ZillyonWeb/faces/colaboradorForm.jsp').as('salvarColaborador');
    
        cy.get(ColaboradorPage.CADASTRAR_NOVO, {timeout: 10000}).click();
        cy.get(ColaboradorPage.NOME_COLABORADOR, { timeout: 10000 }).type(nomeGerado);
        cy.get(ColaboradorPage.DATA_NASCIMENTO).type('10/10/2000');
        cy.get(ColaboradorPage.DADOS_COLABORADOR).click();
        cy.get(ColaboradorPage.TIPO_COLABORADOR).select('Personal Interno');
        cy.get(ColaboradorPage.ADICIONAR).click();
    
        
        cy.get(ColaboradorPage.SALVAR, { timeout: 10000 }).should('be.visible').click();
        cy.wait('@salvarColaborador').then((intercept) => {
            expect(intercept.response?.statusCode).to.eq(200);
        });
        cy.contains('Dados Gravados com Sucesso').should('be.visible');
    
        return nomeGerado;
    }
}


export default new ColaboradorPageClass();
