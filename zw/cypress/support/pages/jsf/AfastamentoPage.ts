import DateUtils from "@utils/DateUtils";
import { AfastamentoPage } from '../../locators';

class AfastamentoPageClass {

capturaPopUp(): void {
    cy.window().then((win) => {
      cy.stub(win, "open", (url) => {
        win.location.href = url;
      }).as("popup");
    });
  }

  abrirAfastamento(): void {
    cy.get(AfastamentoPage.PLANO_ALUNO, { timeout: 10000 })
      .should("be.visible")
      .click();
    this.capturaPopUp();
    cy.get(AfastamentoPage.AFASTAMENTO).click();
  }

  alterarDiasPermitidosFerias(qtdDias: number): void {
    cy.get(AfastamentoPage.ALTERAR_DIAS_PERMITIDOS).click().wait(500);
    cy.get(AfastamentoPage.INPUT_PIN).should("be.visible").type("123");
    cy.get(AfastamentoPage.CONFIRMAR_SENHA).click();
    cy.get(AfastamentoPage.INPUT_DIAS_PERMITIDOS)
      .clear()
      .type(qtdDias.toString());
    cy.get(AfastamentoPage.CONFIRMAR_DIAS_PERMITIDOS).click().wait(500);
  }

  lancarFerias(qtdDias: number, diasPermitidos?: number): void {
    let dataInicial = DateUtils.futureDateBR(1);
    let dataFinal = DateUtils.futureDateBR(qtdDias);

    this.abrirAfastamento();
    cy.get(AfastamentoPage.FERIAS).click();

    if (diasPermitidos >= 0) {
      this.alterarDiasPermitidosFerias(diasPermitidos);
      if (diasPermitidos === 0) {
        cy.get(AfastamentoPage.MENSAGEM_ERRO).contains(
          "Não será possível realizar as férias para este contrato, pois ele não permite dias para férias."
        );
        return;
      }
    }

    cy.get(AfastamentoPage.DATA_INICIAL_FERIAS)
      .should("be.visible")
      .type(dataInicial);
    cy.get(AfastamentoPage.NOME_ALUNO).click().wait(500);
    cy.get(AfastamentoPage.DATA_FINAL_FERIAS)
      .should("be.visible")
      .type(dataFinal);
    cy.get(AfastamentoPage.NOME_ALUNO).click().wait(500);
    cy.get(AfastamentoPage.JUSTIFICATIVA).select("FERIAS");
    cy.get(AfastamentoPage.CONFIRMAR_FERIAS).click();

    if (qtdDias > 0 && qtdDias < 5) {
      cy.contains(
        "O Período das Férias Deve Ser Maior do que a Quantidade Mínima de Férias"
      ).should("be.visible");
      return;
    }

    cy.get(AfastamentoPage.INPUT_PIN).should("be.visible").type("123");
    cy.get(AfastamentoPage.CONFIRMAR_SENHA).click();
    cy.get(AfastamentoPage.BOTAO_IMPRIMIR_COMPROVANTE).should("be.visible");
  }

  cancelarPlanoComDataFutura(data: string): void {
    cy.get(AfastamentoPage.BOTAO_CANCELAMENTO).click();
    cy.get(AfastamentoPage.INPUT_DATA_CANCELAMENTO)
      .clear({ force: true })
      .type(data, { force: true, delay: 0 });
    cy.get(AfastamentoPage.ICONE_CALENDARIO).click();
    cy.get(AfastamentoPage.SELECT_JUSTIFICATIVA).select("SEM JUSTIFICATIVA");
    cy.get(AfastamentoPage.AVANCAR_CANCELAMENTO).click();
    cy.get(AfastamentoPage.DEVOLUCAO).check();
    cy.get(AfastamentoPage.AVANCAR_CANCELAMENTO).click();
    cy.get(AfastamentoPage.AVANCAR_DETALHES_CANCELAMENTO).click();
    cy.get(AfastamentoPage.AVANCAR_CANCELAMENTO).click();
    cy.get(AfastamentoPage.CHECK_QUITACAO_CANCELAMENTO).click();
    cy.get(AfastamentoPage.AVANCAR_CANCELAMENTO).click();
    cy.get(AfastamentoPage.FINALIZAR).click().wait(1500);
    cy.get(AfastamentoPage.INPUT_PIN).type("123");
    cy.get(AfastamentoPage.CONFIRMAR_SENHA).click();
    cy.contains("Operação Realizada com sucesso!").should("be.visible");
  }
}

export default new AfastamentoPageClass();
