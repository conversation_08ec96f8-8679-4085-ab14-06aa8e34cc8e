import { NegociacaoJSFPage } from '../../locators';
class NegociacaoPageClass {

    elements = {
        SELECT_PLANO: '#form\\:plano',
        LOADER: '.rich-mpnl-body',
        ADICIONAR_PRODUTO: '#form\\:btnAdicionarProdutoPlano',
        CONSULTAR: '#formProduto\\:formProduto',
        TABELA_PRODUTOS: '[id="formProduto:resultadoConsultaProduto:tb"]',
        DESCONTO_PRIMEIRO_PRODUTO: '#form\\:planoProdutoVO\\:1\\:descontoPlano > .fa-icon-plus-sign',
        DESCONTO_SEGUNDO_PRODUTO: '#form\\:planoProdutoVO\\:2\\:descontoPlano > .fa-icon-plus-sign',
        INPUT_DESCONTO_MANUAL: '#formDescontoPlanoProdutoSugerido\\:inputValorDescontoManual',
        INPUT_PIN: '#formSenhaAutorizacao\\:senha',
        CONFIRMAR_PIN: '#formSenhaAutorizacao\\:btnAutorizar',
        FECHAR_MODAL_DESCONTO: '#hidelink4',
        VALOR_TOTAL_PRODUTOS: '#form\\:valorTotalProdutos',
    }

    adicionarProduto(produto: string): void {
        cy.get(NegociacaoJSFPage.ADICIONAR_PRODUTO).should('be.visible').click().wait(500)
        cy.get(NegociacaoJSFPage.CONSULTAR).should('be.visible').click()
        cy.get(NegociacaoJSFPage.TABELA_PRODUTOS).contains(produto).click()
        cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible')
    }

    realizarNegociacaoComDescontoProduto(plano: string): void {
        cy.get(NegociacaoJSFPage.SELECT_PLANO).select(plano)
        cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible')
        this.adicionarProduto('VITAMINAS')
        this.adicionarProduto('AUTO VENDAS')

        cy.get(NegociacaoJSFPage.VALOR_TOTAL_PRODUTOS).contains('150,00')

        cy.get(NegociacaoJSFPage.DESCONTO_PRIMEIRO_PRODUTO).click()
        cy.get(NegociacaoJSFPage.INPUT_DESCONTO_MANUAL).should('be.visible').type('2500')
        cy.contains('a', 'Aplicar desconto manual').should('be.visible').click().wait(500)
        cy.get(NegociacaoJSFPage.INPUT_PIN).type('123').wait(500)
        cy.get(NegociacaoJSFPage.CONFIRMAR_PIN).click()
        cy.get(NegociacaoJSFPage.FECHAR_MODAL_DESCONTO).click()
        cy.get(NegociacaoJSFPage.DESCONTO_SEGUNDO_PRODUTO).click()
        cy.get(NegociacaoJSFPage.INPUT_DESCONTO_MANUAL).should('be.visible').type('5000')
        cy.contains('a', 'Aplicar desconto manual').should('be.visible').click().wait(500)
        cy.get(NegociacaoJSFPage.INPUT_PIN).type('123').wait(500)
        cy.get(NegociacaoJSFPage.CONFIRMAR_PIN).click()
        cy.get(NegociacaoJSFPage.FECHAR_MODAL_DESCONTO).click()

        cy.get(NegociacaoJSFPage.VALOR_TOTAL_PRODUTOS).contains('75,00')
    }

}

export default new NegociacaoPageClass();
