import { GestaoNegativacoesPage } from 'locators';
class GestaoNegativacoesPageClass {
    elements = {
        ICONE_CONSULTAR_CLIENTE: '#v20_form\\:v20_consultarCliente',
        BOTAO_CONSULTAR_CLIENTE: '#v20_formCliente\\:v20_btnConsultarCliente',
        DESCRICAO_CLIENTE: '#v20_formCliente\\:v20_valorConsultaCliente',
        MENSAGEM_RETORNO: '.snotifyToast__body ng-star-inserted',
        BOTAO_CONSULTAR: '#v20_form\\:consultar',
        INICIO_VENCIMENTO: ' #v20_form\\:v20_dataInicioVencimentoInputDate',
        TERMINO_VENCIMENTO: '#v20_form\\:v20_dataTerminoVencimentoInputDate',
        LISTAGEM: '#v20_form\\:listagemParcelas',
        LIMPAR_CLIENTE: '#v20_form\\:v20_LimparCliente',
        SITUACAO: '#v20_form\\:v20_situacao'

    }

    pesquisarGestaoNegativacoes(): void {
        cy.wait(3000)
        cy.get(GestaoNegativacoesPage.INICIO_VENCIMENTO, {timeout: 30000}).should('be.visible').clear()
        cy.get(GestaoNegativacoesPage.TERMINO_VENCIMENTO, {timeout: 30000}).should('be.visible').clear()
        cy.get(GestaoNegativacoesPage.ICONE_CONSULTAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.DESCRICAO_CLIENTE, {timeout: 30000}).should('be.visible').type('TESTE REMESSA CIELO')
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.wait(3000)
        cy.contains('TESTE REMESSA CIELO').should('be.visible').click()
        cy.wait(3000)
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.wait(300)
        cy.get(GestaoNegativacoesPage.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'TESTE REMESSA CIELO')
        cy.get(GestaoNegativacoesPage.LIMPAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.SITUACAO, {timeout: 30000}).should('be.visible').select('Pago')
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Pago')
        cy.get(GestaoNegativacoesPage.SITUACAO, {timeout: 30000}).should('be.visible').select('Em Aberto')
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Em Aberto')
        cy.get(GestaoNegativacoesPage.SITUACAO, {timeout: 30000}).should('be.visible').select('Cancelado')
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Cancelado')
        cy.get(GestaoNegativacoesPage.SITUACAO, {timeout: 30000}).should('be.visible').select('Renegociado')
        cy.get(GestaoNegativacoesPage.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(GestaoNegativacoesPage.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Renegociado')
    }

}


export default new GestaoNegativacoesPageClass();
