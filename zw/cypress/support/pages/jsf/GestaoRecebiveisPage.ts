import { GestaoRecebiveisPage } from 'locators';
class GestaoRecebiveisPageClass {
    elements = {

        ABA_CONCILIACAO: '#form\\:link_Conciliacao_Recebiveis',
        BOTAO_REFAZER_CONSULTA: '#form\\:refazerConsulta',
        TIPO_CONCILIACAO: '#formFiltrosGestaoRecebiveis\\:tipoconciliacao',
        BOTAO_CONSULTAR: '#formFiltrosGestaoRecebiveis\\:btnConsultarConciliacao',
        FILTRO: '#form\\:filtros_body',
        ADQUIRENTE: '#formFiltrosGestaoRecebiveis\\:adquirentes',
        CONVENIO: '#formFiltrosGestaoRecebiveis\\:tipoconvenio',
        ABA_CARTAO: '#formFiltrosGestaoRecebiveis\\:cartao',

        TITULO_TELA: '#form\\:linkGestaoRecebiveis > .container-header-titulo',
        FORMAS_PAGAMENTO_0: '#formaPagamento0',
        REFAZER_CONSULTA: '#form\\:refazerConsulta',
        NOME_PAGADOR: '#formFiltrosGestaoRecebiveis\\:nome',
        PESQUISAR: '#formFiltrosGestaoRecebiveis\\:pesquisar',
        LIMPAR_FILTRO: '#formFiltrosGestaoRecebiveis\\:limparFiltros',
        SELECIONAR_PRIMEIRA_TRANSACAO: '#form\\:listaCartoes\\:0\\:cartaoEscolhido',
        BOTAO_MOVIMENTAR: '#form\\:depositar2',

        //MOVIMENTAÇÃO
        DESCRICAO_MOVIMENTACAO: '#formDeposito\\:descricaoDeposito',
        CONTA: '[id="formDeposito:contaSelectitem"]',
        BOTAO_OK: '#formDeposito\\:btnOKModalOperacaoComRecebiveis',
        MENSAGEM: '#messageInfo',
        ABA_NOME_CPF_ALUNO: '#formFiltrosGestaoRecebiveis\\:cpfNomeMatricula',
        INPUT_NOME_CLIENTE: '#formFiltrosGestaoRecebiveis\\:nomeCliente',
        VALOR_FORMA_PAGAMENTO: '#formaPagamentoValor0'
    }

    validarRecebiveisAluno(nomeAluno): void {
        cy.wait(1500)
        // Tela gestão de recebíveis
        cy.get(GestaoRecebiveisPage.TITULO_TELA).should('be.visible').should('contain', 'Gestão de Recebíveis')
        cy.get(GestaoRecebiveisPage.FORMAS_PAGAMENTO_0).should('be.visible').click({force: true})
        cy.contains(new RegExp(`^${nomeAluno}$`, 'i')).should('be.visible');

    }

    limpaFiltro(): void {
        cy.wait(1500)
        cy.get(GestaoRecebiveisPage.LIMPAR_FILTRO).should('be.visible').click({force: true}).wait(1000)
    }

    validarFiltrosConciliacao(): void {
        cy.wait(15000)
        cy.get(GestaoRecebiveisPage.ABA_CONCILIACAO, {timeout: 15000}).should('be.visible').click();
        cy.get(GestaoRecebiveisPage.BOTAO_REFAZER_CONSULTA, {timeout: 15000}).should('be.visible').click({force:true});
        cy.get(GestaoRecebiveisPage.TIPO_CONCILIACAO, {timeout: 15000}).should('be.visible').select('Por Faturamento Recebido')
        cy.get(GestaoRecebiveisPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click({force:true})
        cy.get(GestaoRecebiveisPage.FILTRO, {timeout: 15000}).should('be.visible').should('contain', 'Por Faturamento Recebido');

        ///Conciliacao - Verificar Venda
        cy.get(GestaoRecebiveisPage.BOTAO_REFAZER_CONSULTA, {timeout: 15000}).should('be.visible').click({force:true});
        cy.get(GestaoRecebiveisPage.TIPO_CONCILIACAO, {timeout: 15000}).should('be.visible').select('Por Compensação')
        cy.get(GestaoRecebiveisPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click({force:true})
        cy.get(GestaoRecebiveisPage.FILTRO, {timeout: 15000}).should('be.visible').should('contain', 'Por Compensação');

        ///Conciliacao - Verificar convênio
        cy.get(GestaoRecebiveisPage.BOTAO_REFAZER_CONSULTA, {timeout: 15000}).should('be.visible').click({force:true});
        cy.get(GestaoRecebiveisPage.CONVENIO, {timeout: 15000}).should('be.visible').select('DCC CIELO')
        cy.get(GestaoRecebiveisPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click({force:true})
        cy.get(GestaoRecebiveisPage.FILTRO, {timeout: 15000}).should('be.visible').should('contain', 'DCC CIELO');

       ///Conciliacao - Verificar Cartão
       cy.get(GestaoRecebiveisPage.BOTAO_REFAZER_CONSULTA, {timeout: 15000}).should('be.visible').click({force:true});
       cy.wait(3000)
       cy.get(GestaoRecebiveisPage.ABA_CARTAO, {timeout: 15000}).click({force:true}).click({force:true})
       cy.wait(3000)
       cy.get(GestaoRecebiveisPage.ADQUIRENTE, {timeout: 15000}).select('SEM ADQUIRENTE')
       cy.get(GestaoRecebiveisPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click({force:true})
       cy.get(GestaoRecebiveisPage.FILTRO, {timeout: 15000}).should('contain', 'Sem adquirente');
    }


    realizarMovimentacaoBancaria(nomeAluno): void {
        cy.wait(1500)
        cy.get(GestaoRecebiveisPage.TITULO_TELA).should('be.visible').should('contain', 'Gestão de Recebíveis')
        cy.get(GestaoRecebiveisPage.REFAZER_CONSULTA).should('be.visible').click({force: true})
        this.limpaFiltro()
        cy.get(GestaoRecebiveisPage.NOME_PAGADOR, {timeout:15000}).should('be.visible').clear().type(nomeAluno)
        cy.get(GestaoRecebiveisPage.PESQUISAR).should('be.visible').click({force: true})
        cy.get(GestaoRecebiveisPage.FORMAS_PAGAMENTO_0).should('be.visible').click({force: true})
        cy.contains(nomeAluno).should('be.visible')
        cy.wait(1500)
        cy.get(GestaoRecebiveisPage.SELECIONAR_PRIMEIRA_TRANSACAO).click({ force: true });
        cy.get(GestaoRecebiveisPage.BOTAO_MOVIMENTAR).should('be.visible').click({force: true})
        cy.wait(1500)
        cy.contains('Operação com recebíveis', {timeout:15000})
        cy.get(GestaoRecebiveisPage.DESCRICAO_MOVIMENTACAO, {timeout:15000}).should('be.visible').clear().type('TESTE MOVIMENTACAO')
        cy.get(GestaoRecebiveisPage.CONTA).should('be.visible').then(($select: JQuery<HTMLSelectElement>) => {
            const options: string[] = Array.from($select[0].options).map((opt) => opt.text);
            let selectedOption: string;

            if (options.includes('Banco (Custódia)')) {
                selectedOption = 'Banco (Custódia)';
            } else if (options.includes('Banco da Empresa')) {
                selectedOption = 'Banco da Empresa';
            } else {
                throw new Error('Nenhuma das opções esperadas foi encontrada.');
            }

            cy.wrap($select).select(selectedOption);
            cy.wrap(selectedOption).as('CONTA'); // <-- aqui salvamos como string!
        });

        cy.get(GestaoRecebiveisPage.BOTAO_OK).should('be.visible').click({force: true})
        cy.get(GestaoRecebiveisPage.MENSAGEM).should('be.visible').contains('Dados Gravados com Sucesso')

        cy.get<string>('@CONTA').then((contaSelecionada) => {
            cy.contains(contaSelecionada).should('be.visible');

        });


    }

    buscarRecebiveisCliente(nomeAluno: string, valorRecebiveis: string): void {
        cy.wait(3000)
        cy.get(GestaoRecebiveisPage.REFAZER_CONSULTA).click()
        this.limpaFiltro()
        cy.get(GestaoRecebiveisPage.ABA_NOME_CPF_ALUNO).click()
        cy.get(GestaoRecebiveisPage.INPUT_NOME_CLIENTE).type(nomeAluno)
        cy.get(GestaoRecebiveisPage.PESQUISAR).click()
        cy.get(GestaoRecebiveisPage.VALOR_FORMA_PAGAMENTO, { timeout: 10000 }).should('have.text', valorRecebiveis)
    }

}

export default new GestaoRecebiveisPageClass();
