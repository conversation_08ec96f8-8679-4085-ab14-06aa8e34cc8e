import * as faker from 'faker-br'
import { OrcamentoPage } from '../../locators';

class OrcamentoPageClass {
    elements = {
        MENU_CRIAR_ORCAMENTO: '#sidebar-item-orcamento > .pacto-item-menu > .pacto-item-menu-content',
        SELECT_CONSULTOR: '#form\\:consultor',
        PROSPECTO: '#form\\:nomeCliente',
        PROSPECTO_SELECIONADO: '.rich-sb-cell-padding > .texto-font',
        TIPO_PROSPECTO: '#form\\:paraquem',
        INPUT_TIPO_PROSPECTO: '#form\\:nomeProspecto',
        INPUT_IDADE_PROSPECTO: '#form\\:idade',
        SELECT_ORCAMENTO: '#form\\:modeloorcamento',
        PERIODO: '#form\\:periodo',
        SITUACAO: '#form\\:situacao',
        TIPO_TURMA: '#form\\:tipoturma',
        CONCLUIR: '#form\\:salvar'
    }

    capturaPopUp() {
        cy.window().then((win) => {
            cy.stub(win, "open", (url) => {
                win.location.href = Cypress.config().baseUrl + "/faces/" + url;
            }).as("popup");
        })
    }

    criarOrcamento(): void {
        cy.get(OrcamentoPage.MENU_CRIAR_ORCAMENTO).should('be.visible').click()
        cy.get(OrcamentoPage.SELECT_CONSULTOR).select('PACTO - MÉTODO DE GESTÃO')
        cy.get(OrcamentoPage.PROSPECTO).type('AA_PRIMEIRO CLIENTE')
        cy.get(OrcamentoPage.PROSPECTO_SELECIONADO).type('AA_PRIMEIRO CLIENTE')
        cy.get(OrcamentoPage.TIPO_PROSPECTO).select('Outro').wait(500)
        cy.get(OrcamentoPage.INPUT_TIPO_PROSPECTO).should('be.visible').type(faker.name.findName(), {force: true})
        cy.get(OrcamentoPage.INPUT_IDADE_PROSPECTO).should('be.visible').clear().type('32')
        cy.get(OrcamentoPage.SELECT_ORCAMENTO).select('1')
        cy.get(OrcamentoPage.PERIODO).select('Todos os períodos')
        cy.get(OrcamentoPage.SITUACAO).select('Andamento')
        cy.get(OrcamentoPage.TIPO_TURMA).select('Todas')
        cy.get(OrcamentoPage.CONCLUIR).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')
    }


}

export default new OrcamentoPageClass();
