import { CobrancaAutoBloqueadaPage } from '../../locators';
class CobrancaAutoBloqueadaPageClass {

    elements = {
        BOTAO_CONSULTAR: '#form\\:consultarClientes',
        EMPRESA: '.font-size-em-max > .cb-container > select',
    }

    validarRelatorio(nomeAluno: string): void {

        cy.get(CobrancaAutoBloqueadaPage.EMPRESA, {timeout: 50000})
            .should('be.visible')
            .select('NOME FANTASIA DA ACADEMIA')
        cy.get(CobrancaAutoBloqueadaPage.BOTAO_CONSULTAR, {timeout: 50000})
            .should('be.visible').click()
        cy.wait(5000)
        cy.contains(nomeAluno)

    }
}

export default new CobrancaAutoBloqueadaPageClass();
