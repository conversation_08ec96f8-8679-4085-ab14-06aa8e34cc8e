import { ContatoPessoalPage } from '../../locators';

class ContatoPage {
    elements = {
        BOTAO_CONSULTAR: '#formRC\\:consultar',
        TIPO_CONTATO: '#form\\:tipoOperacao',
        COMENTARIO: '#form\\:observacaoHistorico',
        BOTAO_SIMPLES_REGISTRO: '#form\\:btnSimplesRegistro',
        HISTORICO_LIGACOES: '#modalHistoricoContadorClienteContentTable',
        CONSULTAR: '#formRC\\:valorConsulta',
        OBJECAO: '#form\\:btnObjecao',
        SELECIONAR_OBJECAO: '#form\\:objecao',
        BOTAO_CONCLUIR_OBJECAO: '#form\\:concluirObjecao',
        LINK_CARTAO: '#form\\:linkCadastroCartaoOnline',
        ADD_TAG_EMAIL: '#form\\:tagEmail',
        ENVIAR_MAIL: '#form\\:gravarEnviarEmail',
        TAG_EMPRESA: '#formMarcadorEmail\\:MarcadoEmail\\:0\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        TAG_CIDADE_ESTADO: '#formMarcadorEmail\\:MarcadoEmail\\:1\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        TAG_TELEFONE: '#formMarcadorEmail\\:MarcadoEmail\\:2\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        TAG_SITE: '#formMarcadorEmail\\:MarcadoEmail\\:3\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        TAG_LOGO: '#formMarcadorEmail\\:MarcadoEmail\\:4\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        TAG_NOME: '#formMarcadorEmail\\:MarcadoEmail\\:5\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign',
        SELECIONAR_PESQUISA: '#form\\:pesquisa',
        TITULO_EMAIL: '#form\\:tituloEmail',
        BOTAO_SIM: '#formMdlMensagemGenerica\\:sim'
    }

    enviarContatoTelefonico(): void {
        cy.get(ContatoPessoalPage.TIPO_CONTATO, {timeout: 20000}).should('be.visible').select('Contato Telefônico')
        cy.get(ContatoPessoalPage.COMENTARIO, {timeout: 20000}).should('be.visible').clear().type('TESTE CONTATO TELEFONICO')
        cy.get(ContatoPessoalPage.BOTAO_SIMPLES_REGISTRO, {timeout: 20000}).should('be.visible').click({force: true})

    }
    validarContatoTelefonico(): void {
        cy.wait(3000)
        cy.contains('h6', 'LIGAÇÕES')
        .parent()
        .find('a')
        .click({ force: true });
     cy.get(ContatoPessoalPage.HISTORICO_LIGACOES, {timeout: 20000}).should('be.visible').should('contain','TESTE CONTATO TELEFONICO')


    }

    consultarAluno(nomeAluno): void {
        cy.wait(3000)
        cy.get(ContatoPessoalPage.CONSULTAR, {timeout: 20000}).should('be.visible').clear().type(nomeAluno)
        cy.get(ContatoPessoalPage.BOTAO_CONSULTAR, {timeout: 20000}).should('be.visible').click({force: true})
        cy.wait(3000)
        cy.contains(nomeAluno, {timeout: 20000}).click()
        cy.window().then((win) => {
            cy.stub(win, 'open').callsFake((url) => {
               win.location.href = url;
               });
          });
        cy.wait(3000)
        cy.contains('Contato avulso', {timeout: 20000}).should('be.visible')

    }



    linkCadastroCartao(): void {
        
        cy.get(ContatoPessoalPage.LINK_CARTAO, {timeout: 20000}).should('be.visible').click()
        cy.get(ContatoPessoalPage.COMENTARIO, {timeout: 20000}).should('be.visible')
        .should('contain','Este é o seu link para cadastrar o cartão de crédito na empresa: NOME FANTASIA DA ACADEMI')


    }
    contatoEmail(): void {
        cy.wait(3000)
        cy.get(ContatoPessoalPage.TIPO_CONTATO, {timeout: 20000}).should('be.visible').select('Contato E-mail')
        cy.get(ContatoPessoalPage.ENVIAR_MAIL, {timeout: 20000}).should('be.visible')
       
    }
     
    addTAGS(): void {

     this.contatoEmail()
        cy.wait(3000)
        cy.get(ContatoPessoalPage.TITULO_EMAIL, {timeout: 20000}).should('be.visible').clear().type('TESTE TITULO')

        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_EMPRESA, {timeout: 20000}).should('be.visible').click()
        cy.contains('ENDERECO_TAG', {timeout: 20000})

        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_CIDADE_ESTADO, {timeout: 20000}).should('be.visible').click()
        cy.contains('CIDADE_ESTADO_TAG', {timeout: 20000})
       
        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_TELEFONE, {timeout: 20000}).should('be.visible').click()
        cy.contains('TELEFONE_TAG', {timeout: 20000})

        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_SITE, {timeout: 20000}).should('be.visible').click()
        cy.contains('WEB_SITE_TAG', {timeout: 20000})

        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_LOGO, {timeout: 20000}).should('be.visible').click()
        cy.contains('LOGO_EMPRESA_TAG', {timeout: 20000})

        cy.get(ContatoPessoalPage.ADD_TAG_EMAIL, {timeout: 20000}).should('be.visible').click()
        cy.contains('Marcadores de Email', {timeout: 20000})
        cy.get(ContatoPessoalPage.TAG_NOME, {timeout: 20000}).should('be.visible').click()
        cy.contains('NOME_EMPRESA', {timeout: 20000})
    
    }
    

    utilizarModeloMensagem(): void {
        cy.contains('Utilizar como Modelo de Mensagem', {timeout: 20000}).should('be.visible').click({force: true})
        cy.wait(300)
        cy.contains('Será criado um modelo de mensagem a partir desse email. Deseja continuar ?', {timeout: 20000}).should('be.visible')
        cy.get(ContatoPessoalPage.BOTAO_SIM, {timeout: 20000}).should('be.visible').click()
        cy.wait(300)
        cy.contains('Modelo de Mensagem gravado com sucesso.', {timeout: 20000}).should('be.visible')
      
    }
 

}


export default new ContatoPage();
