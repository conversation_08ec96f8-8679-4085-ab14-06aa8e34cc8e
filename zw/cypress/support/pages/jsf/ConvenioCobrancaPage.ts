import * as faker from 'faker-br';
import { ConvenioCobrancaPage } from '../../locators';

class ConvenioCobrancaPageClass {

    elements = {
        ITENS_PAGINA: '#tblConvenioCobranca_lengthId',

        //Convênio de desconto
        EDITAR_VIGENCIA_FINAL: '#cat-datepicker-2-input',

        //Cadastro de convênio
        BOTAO_NOVO: '#form\\:btnNovo',
        TIPO_CONVENIO: '#form\\:tipoConvenio',
        DESCRICAO: '#form\\:descricao',
        EMPRESA: '#form\\:contaEmpresa',
        BOTAO_GRAVAR: '#form\\:salvar',
        CNPJ: '#form\\:CNPJ',
        MENSAGEM_DETALHADA: '.mensagemDetalhada',
        NUMERO_CONTRATO: '#form\\:numeroContrato',
        BOTAO_EXCLUIR: '#form\\:excluir',
        BOTAO_SIM_EXCLUIR: '#formMdlMensagemGenerica\\:sim',
        MENSAGEM: '.mensagem',
        CODIGO_AUTENTICACAO01: '#form\\:codigoAutenticacao01',
        CODIGO_AUTENTICACAO02: '#form\\:codigoAutenticacao02',
        CODIGO_AUTENTICACAO03: '#form\\:codigoAutenticacao03',
        CODIGO_AUTENTICACAO04: '#form\\:codigoAutenticacao04',
        CODIGO_AUTENTICACAO05: '#form\\:codigoAutenticacao05',
        CODIGO_AUTENTICACAO06: '#form\\:codigoAutenticacao06',
        CODIGO_AUTENTICACAO07: '#form\\:codigoAutenticacao07',
        BOTAO_CONSULTAR: '#form\\:consultar',
        CIELO_HOMOLOGACAO: '#form\\:gerarConvenioCieloSandbox',
        LOG: '#form\\:btnLog > .btn-print-2',
        DIAS_PIX: '#form\\:diasExpirarPix',
        CHAVE1_PIX_PJBANK: '#form\\:chavePJBank',
        CHAVE2_PIX_PJBANK: '#form\\:credencialPJBank'
    }

    editarConvenioDeDesconto(): void {
        cy.contains('CONVÊNIO COM ISENÇÃO').click()
        cy.get(ConvenioCobrancaPage.EDITAR_VIGENCIA_FINAL).type('17/03/2099')
        cy.contains('Salvar').click()
        cy.contains('Convênio de desconto salvo com sucesso.').should('be.visible')
    }


    cadastrarConvenioPinpad(): string {
        const nomeConvenio = `PinPad - GetCard (Scope) ${faker.random.number()}`
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.BOTAO_NOVO, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.TIPO_CONVENIO, {timeout: 15000}).should('be.visible').select('PinPad - GetCard (Scope)')
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').type(nomeConvenio, {force: true})
        cy.get(ConvenioCobrancaPage.CNPJ).should('be.visible').type('**************')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO01).should('be.visible').type('3825000111')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO02).should('be.visible').type('3825000111')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        return nomeConvenio
    }

    excluirConvenio(nomeConvenio): void {

        this.selecionarTodos()
        cy.wait(3000)
        cy.contains(nomeConvenio, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.BOTAO_EXCLUIR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.BOTAO_SIM_EXCLUIR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados Excluídos com Sucesso')

    }

    selecionarTodos(): void {
        cy.get(ConvenioCobrancaPage.ITENS_PAGINA).select('Todos')
    }


    cadastrarConvenioBoletoCaixaOnline(): void {
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.BOTAO_NOVO, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.TIPO_CONVENIO, {timeout: 15000}).should('be.visible').select('Boleto Bancário Caixa - Registro Online')
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').type('CAIXA REGISTRO ONLINE', {force: true})
        cy.get(ConvenioCobrancaPage.EMPRESA, {timeout: 15000}).should('be.visible').select('AG:111-0/ CC:1111-0')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM_DETALHADA, {timeout: 15000}).should('be.visible').should('contain', 'O campo "CNPJ" deve ser informado.')
        cy.get(ConvenioCobrancaPage.CNPJ).should('be.visible').type('**************')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM_DETALHADA, {timeout: 15000}).should('be.visible').should('contain', 'O campo "Num. Contrato/Num. Estabelecimento" deve ser informado.')
        cy.get(ConvenioCobrancaPage.NUMERO_CONTRATO).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)

    }

    editarDescricaoConvenio(nomeConvenio, descricaoEditada): void {
        this.selecionarTodos()
        cy.wait(3000)
        cy.contains(nomeConvenio, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').clear().type(descricaoEditada, {force: true})
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
    }

    cadastrarConvenioPinBank(): void {
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.BOTAO_NOVO, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.TIPO_CONVENIO, {timeout: 15000}).should('be.visible').select('DCC PinBank Online')
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').type('DCC PINBANK ONLINE', {force: true})
        cy.get(ConvenioCobrancaPage.CNPJ).should('be.visible').type('**************')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO01).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO02).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO03).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO04).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO05).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO06).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO07).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)

    }

    cadastrarConvenioGetNet(): void {
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.BOTAO_NOVO, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.TIPO_CONVENIO, {timeout: 15000}).should('be.visible').select('DCC Getnet Online')
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').type('DCC GETNET ONLINE', {force: true})
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO01).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO02).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.CODIGO_AUTENTICACAO03).should('be.visible').type('**********')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)

    }

    cadastrarConvenioCieloHomologacao(): void {
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.BOTAO_NOVO, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.CIELO_HOMOLOGACAO, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Informe os Dados')
        cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, {timeout: 15000}).should('be.visible').click()
        cy.get(ConvenioCobrancaPage.MENSAGEM, {timeout: 15000}).should('be.visible').should('contain', 'Dados gravados com sucesso!')
        cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, {timeout: 15000}).should('be.visible').click()
        cy.wait(3000)

    }


    validarLogConvenioCobranca(validacaoLOG): void {
        cy.wait(3000)
     cy.get(ConvenioCobrancaPage.LOG, {timeout: 15000}).should('be.visible').click()
     cy.window().then((win) => {
        cy.stub(win, 'open').callsFake((url) => {
          win.location.href = url; // Redireciona para a mesma aba
        });
      })


      cy.contains(validacaoLOG).should('be.visible')
    }

 
    cadastrarConvenioPixProducao(): void {
     this.selecionarTodos();
     cy.wait(3000);

        cy.document().then((doc: Document) => {
         const textoCompleto = doc.body.innerText;

              if (textoCompleto.includes('PIX PRODUCAO CAIXA ABERTO')) {
              cy.log('Já existe um PIX PRODUCAO CAIXA ABERTO. Teste interrompido.');
              return;
             }

         // Se não encontrar, segue o fluxo
         cy.get(ConvenioCobrancaPage.BOTAO_NOVO, { timeout: 15000 }).should('be.visible').click();

         cy.get(ConvenioCobrancaPage.TIPO_CONVENIO, { timeout: 15000 })
         .should('be.visible')
         .should('contain', 'Pix PjBank')
         .select('Pix PjBank');

         cy.wait(3000);

         cy.get(ConvenioCobrancaPage.DESCRICAO).should('be.visible').type('PIX PRODUCAO CAIXA ABERTO', { force: true });
         cy.get('#form\\:diasExpirarPix').should('be.visible').type('1');
         cy.get('#form\\:chavePJBank').should('be.visible').type('c90e92b80a7ae70b5224ec7952bce2824b799530');
         cy.get('#form\\:credencialPJBank').should('be.visible').type('db7a488abbfdc113850462cf7e288ac3629c8d9d');

         cy.get(ConvenioCobrancaPage.BOTAO_GRAVAR, { timeout: 15000 }).should('be.visible').click();
 
         cy.get(ConvenioCobrancaPage.MENSAGEM, { timeout: 15000 })
         .should('be.visible')
          .should('contain', 'Dados gravados com sucesso!');

         cy.get(ConvenioCobrancaPage.BOTAO_CONSULTAR, { timeout: 15000 }).should('be.visible').click();
         cy.wait(3000);
      
         });
    }




}


export default new ConvenioCobrancaPageClass();
