import { GestaoRemessaPage } from 'locators';
class GestaoRemessaPageClass {
    elements = {
        BOTAO_CONSULTAR: '#form\\:consultarRemessas',
        FECHAR_REMESSA: '#form\\:tblRemessas\\:0\\:fecharRemessa',
        BOTAO_SIM_FECHAR_REMESSA: '#formMdlMensagemGenerica\\:sim'
    }

    fecharRemessa(): void {
        cy.wait(3000)
        cy.get(GestaoRemessaPage.BOTAO_CONSULTAR, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(GestaoRemessaPage.FECHAR_REMESSA, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(GestaoRemessaPage.BOTAO_SIM_FECHAR_REMESSA, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(GestaoRemessaPage.FECHAR_REMESSA, {timeout: 10000}).should('not.exist');
        //Não deve aparecer o botão de fechar, após ser fechado a remessa
    }
}

export default new GestaoRemessaPageClass();
