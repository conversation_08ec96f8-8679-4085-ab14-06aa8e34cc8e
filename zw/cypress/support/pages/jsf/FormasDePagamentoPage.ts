import * as faker from 'faker-br'
import { TelaFormasDePagamentoPage } from 'locators';

class FormasDePagamentoPageClass {

    elements = {
        DIV_PAGAMENTO_ESCOLHIDO: '#divFormaPagamentoEscolhido',
        EDITAR_DATA: '.fa-icon-edit',
        LIMPAR_DATA: '.fa-icon-eraser',
        DINHEIRO: '#form\\:opcoesFormasPagamento\\:0\\:movPagamentoEscolhido',
        BOLETO: '#form\\:opcoesFormasPagamento\\:1\\:movPagamentoEscolhido',
        CARTAO_CREDITO: '#form\\:opcoesFormasPagamento\\:2\\:movPagamentoEscolhido',
        CARTAO_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:movPagamentoEscolhido',
        CHEQUE: '#form\\:opcoesFormasPagamento\\:4\\:movPagamentoEscolhido',
        PIX: '#form\\:opcoesFormasPagamento\\:5\\:movPagamentoEscolhido',
        DEBITO: '#form\\:opcoesFormasPagamento\\:6\\:movPagamentoEscolhido',
        BOTAO_CONFIRMAR: '#form\\:btnConfirmar',
        INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
        CONFIRMAR_SENHA: '#formSenhaAutorizacao\\:btnAutorizar',
        MSG_CONFIRMACAO: '#form\\:msgVerdeConfirmacao1',
        CONVENIO_BOLETO: '#form\\:opcoesFormasPagamento\\:1\\:convenio',
        ADQUIRENTE: '#form\\:opcoesFormasPagamento\\:2\\:adquirente',
        OPERADORA_CARTAO: '#form\\:opcoesFormasPagamento\\:2\\:operadoraCartao',
        PARCELAS: '#form\\:opcoesFormasPagamento\\:2\\:nrParcelaCartao',
        AUTORIZACAO: '#form\\:opcoesFormasPagamento\\:2\\:nrParcelaCartao',
        ADIQUIRENTE_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:adquirente',
        OPERADORA_CARTAO_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:operadoraCartao1',
        AUTORIZACAO_DEBITO: ':nth-child(4) > .inputTextClean',
        LOADER: '.rich-mpnl-body > img',
        TITULO_PAGINA: '.margin-box > .container-header-titulo',
        CADASTRAR_NOVO: '#form\\:btnNovo',
        DESCRICAO_FORMA_PAGAMENTO: '#form\\:descricao',
        TIPO_PAGAMENTO: '#form\\:tipoFormaPagamento',
        CONVENIO: '#form\\:convenio',
        EMPRESA: '#form\\:empresaselecionadafpgto > tbody > .linhaImpar > .classDireita > .block > .form',
        CONTA:'#form\\:contadestino',
        ADICIONAR_EMPRESA: '#form\\:adicionarCfgEmpresa',
        PERFIL_ACESSO:'#form\\:perfilAcesso',
        ADICIONAR_PERFIL_ACESSO: '#form\\:adicionarFormaPagamentoPerfilAcesso',
        SALVAR:'#form\\:salvar',
        VOLTAR: '#form\\:consultar',
        MENSAGEM_RETORNO: '#titleInfo',
        OPCAO_PAGAMENTO: '.opcaoPagamento',
        BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
        INPUT_DESCRICAO: '#form\\:descricao',
        SELECT_TIPO_FORMA_PAGAMENTO: '#form\\:tipoFormaPagamento',
        CHECK_SOMENTE_FINANCEIRO: '#form\\:somenteFinanceiro',
        SELECT_PINPAD: '#form\\:pinpad',
        INPUT_DESCRICAO_PDV: '#form\\:descricaoPinpadCappta',
        INPUT_SERIAL_PDV: '#form\\:serialNumberCappta',
        CHECK_RECEBER_SOMENTE_PDV: ':nth-child(5) > .classDireita > input',
        BOTAO_ADICIONAR_PDV: '#form\\:adicionarPinpad',
        BOTAO_GRAVAR: '#form\\:salvar'
    }

    aguardarCarregamento() {
        cy.get(TelaFormasDePagamentoPage.TITULO_PAGINA, {timeout: 20000}).should('be.visible')
        cy.get(TelaFormasDePagamentoPage.TITULO_PAGINA).contains('Formas de Pagamento')
    }

    receberParcela(formaPagamento: string, semSenha: boolean = false) {
        cy.intercept('GET', '/ZillyonWeb/faces/pagamento.jsp').as('getPagamento')
        cy.intercept('POST', '/ZillyonWeb/faces/pagamento.jsp').as('postPagamento')
    
        cy.wait('@getPagamento', { timeout: 10000 })
        cy.get(TelaFormasDePagamentoPage.DIV_PAGAMENTO_ESCOLHIDO).should('be.visible', { timeout: 10000 }).wait(1000)
    
        switch (formaPagamento.toUpperCase()) {
            case 'DINHEIRO':
                cy.get(TelaFormasDePagamentoPage.DINHEIRO).check()
                break
    
            case 'BOLETO':
                cy.get(TelaFormasDePagamentoPage.BOLETO).should('exist').check()
                cy.get(TelaFormasDePagamentoPage.CONVENIO_BOLETO).should('be.visible').select('BOLETO')
                break
    
            case 'CARTÃO DE CRÉDITO':
                
                cy.get(TelaFormasDePagamentoPage.CARTAO_CREDITO).should('exist').check()
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')
                cy.get(TelaFormasDePagamentoPage.ADQUIRENTE).should('be.visible').select('STONE')
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')
                cy.get(TelaFormasDePagamentoPage.OPERADORA_CARTAO).should('be.visible').select('VISA (CRÉDITO)')
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')
                cy.get(TelaFormasDePagamentoPage.PARCELAS).should('be.visible').select('12 vezes')
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')

                if (semSenha) {
                    cy.get(TelaFormasDePagamentoPage.AUTORIZACAO).should('be.visible').type(faker.random.number())
                }
                break
    
            case 'CARTÃO DE DÉBITO':
                cy.get(TelaFormasDePagamentoPage.CARTAO_DEBITO).should('exist').check({ force: true })
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')
                cy.get(TelaFormasDePagamentoPage.ADIQUIRENTE_DEBITO).should('be.visible').select('STONE')
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')
                cy.get(TelaFormasDePagamentoPage.OPERADORA_CARTAO_DEBITO).should('be.visible').select('MAESTRO (DEBITO)')
                cy.get(TelaFormasDePagamentoPage.LOADER).should('not.be.visible')

                if (semSenha) {
                    cy.get(TelaFormasDePagamentoPage.AUTORIZACAO_DEBITO).should('be.visible').type(faker.random.number())
                }
                break
        }
    
        cy.wait('@postPagamento', { timeout: 10000 })
        cy.get(TelaFormasDePagamentoPage.BOTAO_CONFIRMAR).should('be.visible').click({ force: true })
    
        if (!semSenha) {
            cy.get(TelaFormasDePagamentoPage.INPUT_SENHA).should('be.visible').clear()
            cy.get(TelaFormasDePagamentoPage.INPUT_SENHA).type('123', { force: true })
            cy.get(TelaFormasDePagamentoPage.CONFIRMAR_SENHA).should('be.visible').click({ force: true })
        }
    
        cy.get(TelaFormasDePagamentoPage.MSG_CONFIRMACAO).contains('Pagamento Efetuado Com Sucesso.')
    }

    cadastrarFormDePagamento() {
        cy.wait(5000)
        
        const descricaoAleatoria = `Teste Auto ${Date.now()}`;

        cy.get(TelaFormasDePagamentoPage.CADASTRAR_NOVO).click();
        cy.get(TelaFormasDePagamentoPage.DESCRICAO_FORMA_PAGAMENTO).type(descricaoAleatoria);
        cy.get(TelaFormasDePagamentoPage.TIPO_PAGAMENTO).select('Pix')

        cy.wait(300)
        cy.get(TelaFormasDePagamentoPage.CONVENIO).should('contain', 'PIX BANCO DO BRASIL').select('PIX BANCO DO BRASIL');

        cy.get(TelaFormasDePagamentoPage.EMPRESA).select('NOME FANTASIA DA ACADEMIA')
        cy.get(TelaFormasDePagamentoPage.CONTA).select('B:1 / AG:111-0/ CC:1111-0');

        cy.get(TelaFormasDePagamentoPage.ADICIONAR_EMPRESA).click()

        cy.wait(300)
        cy.get(TelaFormasDePagamentoPage.SALVAR, { timeout: 10000 }).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')

        cy.get(TelaFormasDePagamentoPage.VOLTAR).should('be.visible').click()

    }

    editarFormaDePagamento(){
        cy.wait(5000)

        cy.contains('BOLETO BANCÁRIO', { timeout: 10000 }).should('be.visible').click()

        cy.get(TelaFormasDePagamentoPage.EMPRESA).select('NOME FANTASIA DA ACADEMIA')
        cy.get(TelaFormasDePagamentoPage.ADICIONAR_EMPRESA).click()

        cy.wait(300)
        cy.get(TelaFormasDePagamentoPage.SALVAR, { timeout: 10000 }).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')

    }

    selecionarFormaPagamento(nomeFormaPagamento) {
        cy.wait(3000);
        cy.get(TelaFormasDePagamentoPage.OPCAO_PAGAMENTO)
            .contains(nomeFormaPagamento)
            .parents(TelaFormasDePagamentoPage.OPCAO_PAGAMENTO)
            .find('input[type="checkbox"]')
            .check({ force: true });

    }


    validarBloqueioPinpad(formaPagamento: string) {
        cy.wait(3000);
        this.selecionarFormaPagamento(formaPagamento)
        cy.get(TelaFormasDePagamentoPage.BOTAO_CONFIRMAR).should('be.visible').click({ force: true })
        cy.get(TelaFormasDePagamentoPage.MENSAGEM_RETORNO, { timeout: 10000 }).should('be.visible').contains('Não Foi Possível Realizar esta Operação')
    }

    incluirTipoSomentePinpad(): string {
        const nomeForma = `F. PAGAMENTO ${faker.random.number()}`
        cy.get(TelaFormasDePagamentoPage.BOTAO_CADASTRAR_NOVO).click()
        cy.get(TelaFormasDePagamentoPage.INPUT_DESCRICAO).type(nomeForma)
        cy.get(TelaFormasDePagamentoPage.SELECT_TIPO_FORMA_PAGAMENTO).select('Cartão de Crédito').wait(1500)
        cy.get(TelaFormasDePagamentoPage.CHECK_SOMENTE_FINANCEIRO).uncheck()

        cy.get(TelaFormasDePagamentoPage.SELECT_PINPAD).select('Linx')
        cy.get(TelaFormasDePagamentoPage.INPUT_DESCRICAO_PDV).type(nomeForma)
        cy.get(TelaFormasDePagamentoPage.INPUT_SERIAL_PDV).type('123456')
        cy.get(TelaFormasDePagamentoPage.CHECK_RECEBER_SOMENTE_PDV).check()
        cy.get(TelaFormasDePagamentoPage.BOTAO_GRAVAR).click()
        return nomeForma
    }

    validarRecebimentoSomentePinpad(formaPagamento: string): void {
        cy.wait(5000)
        cy.contains(formaPagamento ,{ timeout: 10000 }).click().wait(1500)
        cy.get(TelaFormasDePagamentoPage.BOTAO_CONFIRMAR).click()
        cy.contains(`A forma de pagamento ${formaPagamento} deve ser recebida somente via pinpad.`)
    }


}

export default new FormasDePagamentoPageClass();
