import { QuestionarioPage } from 'locators';
class QuestionarioPageClass {
    elements = {
        EMAGRECIMENTO: '#form\\:questionarioCliente\\:0\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        MUSCULACAO: '#form\\:questionarioCliente\\:1\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        MANHA: '#form\\:questionarioCliente\\:2\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        AMIGOS: '#form\\:questionarioCliente\\:3\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        SELECT_CONSULTOR: '#form\\:consultor',
        ATUALIZAR_BV: '#form\\:cadastrarVisitante'
    }

    atualizarBV(): void {
        cy.get(QuestionarioPage.SELECT_CONSULTOR, {timeout: 10000}).should('be.visible');
        cy.get(QuestionarioPage.SELECT_CONSULTOR).select('PACTO - MÉTODO DE GESTÃO')
        cy.get(QuestionarioPage.EMAGRECIMENTO).click()
        cy.get(QuestionarioPage.MUSCULACAO).click()
        cy.get(QuestionarioPage.MANHA).click()
        cy.get(QuestionarioPage.AMIGOS).click()
        cy.get(QuestionarioPage.ATUALIZAR_BV).click()

    }
}

export default new QuestionarioPageClass();
