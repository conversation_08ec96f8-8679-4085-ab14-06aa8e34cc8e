.clear_deploy_template: &clear_deploy_template
  stage: finish
  interruptible: true 
  script:
    - npm i
    - node scripts/remove-stack.js -s $CI_PIPELINE_ID      
      
clear-deploy-staging:
  <<: *clear_deploy_template
  rules:
    - if: $TEST_ENVIRONMENT == "staging"
  tags:
    - deploy-teste-auto

clear-deploy-oci-staging:
  <<: *clear_deploy_template
  rules:
    - if: $TEST_ENVIRONMENT == "staging"
  tags:
    - deploy-teste-auto-oci-shell

clear-deploy-prod:
  <<: *clear_deploy_template
  rules:
    - if: $TEST_ENVIRONMENT == "prod"
  tags:
    - swarm
    - locaweb
    - manager

clear-deploy-oci-prod:
  <<: *clear_deploy_template
  rules:
    - if: $TEST_ENVIRONMENT == "prod"
  tags:
    - deploy-teste-auto-oci-shell

clear-deploy-oci-prod-t3:
  <<: *clear_deploy_template
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod-t3"
  tags:
    - deploy-teste-auto-oci-t3-shell
    
