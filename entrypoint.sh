#!/bin/bash

echo "🚀 [CYPRESS ENTRYPOINT] Iniciando execução do Cypress..."
echo "📋 [CYPRESS ENTRYPOINT] Verificando variáveis de ambiente obrigatórias..."

if [ -z "$CYPRESS_RECORD_KEY" ]; then
  echo "❌ [CYPRESS ENTRYPOINT] CYPRESS_RECORD_KEY is not set. Exiting."
  exit 1
fi
echo "✅ [CYPRESS ENTRYPOINT] CYPRESS_RECORD_KEY está definida"

if [ -z "$CYPRESS_CI_BUILD_ID" ]; then
  echo "❌ [CYPRESS ENTRYPOINT] CYPRESS_CI_BUILD_ID is not set. Exiting."
  exit 1
fi
echo "✅ [CYPRESS ENTRYPOINT] CYPRESS_CI_BUILD_ID está definida"

echo ""
echo "🌐 [CYPRESS ENTRYPOINT] Variáveis de ambiente importantes:"
echo "   - CYPRESS_SORRY: $CYPRESS_SORRY"
echo "   - TEST_ENVIRONMENT: $TEST_ENVIRONMENT"
echo "   - URL_DISCOVERY: $URL_DISCOVERY"
echo "   - URL_CYPRESS_SORRY_API: $URL_CYPRESS_SORRY_API"
echo "   - URL_CYPRESS_SORRY_DIRECTOR: $URL_CYPRESS_SORRY_DIRECTOR"
echo "   - CYPRESS_RECORD_KEY: ${CYPRESS_RECORD_KEY:0:10}..."
echo "   - CYPRESS_CI_BUILD_ID: $CYPRESS_CI_BUILD_ID"
echo "   - CYPRESS_REPLICAS: $CYPRESS_REPLICAS"
echo "   - CYPRESS_SPEC: $CYPRESS_SPEC"
echo "   - CYPRESS_TAGS: $CYPRESS_TAGS"
echo "   - CYPRESS_BURN: $CYPRESS_BURN"
echo "   - CI_COMMIT_MESSAGE: $CI_COMMIT_MESSAGE"

echo ""
echo "🔧 [CYPRESS ENTRYPOINT] Processando parâmetros opcionais..."

if [ ! -z "$CYPRESS_SPEC" ]; then
  CYPRESS_SPEC_PARAM="--spec $CYPRESS_SPEC"
  echo "✅ [CYPRESS ENTRYPOINT] CYPRESS_SPEC_PARAM: $CYPRESS_SPEC_PARAM"
else
  echo "ℹ️ [CYPRESS ENTRYPOINT] CYPRESS_SPEC não definida, executando todos os testes"
fi

if [ ! -z "$CYPRESS_TAGS" ]; then
  CYPRESS_TAGS_PARAM="--tag $CYPRESS_TAGS"
  echo "✅ [CYPRESS ENTRYPOINT] CYPRESS_TAGS_PARAM: $CYPRESS_TAGS_PARAM"
else
  echo "ℹ️ [CYPRESS ENTRYPOINT] CYPRESS_TAGS não definida"
fi

if [ ! -z "$CYPRESS_BURN" ]; then
  CYPRESS_BURN_PARAM=",burn=$CYPRESS_BURN"
  echo "✅ [CYPRESS ENTRYPOINT] CYPRESS_BURN_PARAM: $CYPRESS_BURN_PARAM"
else
  echo "ℹ️ [CYPRESS ENTRYPOINT] CYPRESS_BURN não definida"
fi

if [ ! -z "$CI_COMMIT_MESSAGE" ]; then
  CI_COMMIT_MESSAGE_ENV=",CI_COMMIT_MESSAGE=$CI_COMMIT_MESSAGE"
  echo "✅ [CYPRESS ENTRYPOINT] CI_COMMIT_MESSAGE_ENV: $CI_COMMIT_MESSAGE_ENV"
else
  echo "ℹ️ [CYPRESS ENTRYPOINT] CI_COMMIT_MESSAGE não definida"
fi
  

echo ""
echo "📋 [CYPRESS ENTRYPOINT] Verificando arquivo de configuração..."
if [ -f "/e2e/currents.config.js" ]; then
  echo "✅ [CYPRESS ENTRYPOINT] Arquivo currents.config.js encontrado"
  echo "📄 [CYPRESS ENTRYPOINT] Conteúdo do currents.config.js:"
  cat /e2e/currents.config.js
else
  echo "❌ [CYPRESS ENTRYPOINT] Arquivo currents.config.js não encontrado"
fi

echo ""
echo "🔗 [CYPRESS ENTRYPOINT] Testando conectividade com serviços do Cypress Sorry..."

# Testa conectividade com o Director
if [ ! -z "$URL_CYPRESS_SORRY_DIRECTOR" ]; then
  echo "🌐 [CYPRESS ENTRYPOINT] Testando Director: $URL_CYPRESS_SORRY_DIRECTOR"
  if curl -s --connect-timeout 10 "$URL_CYPRESS_SORRY_DIRECTOR" > /dev/null; then
    echo "✅ [CYPRESS ENTRYPOINT] Director está acessível"
  else
    echo "❌ [CYPRESS ENTRYPOINT] Director não está acessível ou demorou para responder"
  fi
else
  echo "⚠️ [CYPRESS ENTRYPOINT] URL_CYPRESS_SORRY_DIRECTOR não definida"
fi

# Testa conectividade com a API
if [ ! -z "$URL_CYPRESS_SORRY_API" ]; then
  echo "🌐 [CYPRESS ENTRYPOINT] Testando API: $URL_CYPRESS_SORRY_API"
  if curl -s --connect-timeout 10 "$URL_CYPRESS_SORRY_API" > /dev/null; then
    echo "✅ [CYPRESS ENTRYPOINT] API está acessível"
  else
    echo "❌ [CYPRESS ENTRYPOINT] API não está acessível ou demorou para responder"
  fi
else
  echo "⚠️ [CYPRESS ENTRYPOINT] URL_CYPRESS_SORRY_API não definida"
fi

echo ""
echo "🎯 [CYPRESS ENTRYPOINT] Determinando modo de execução..."

if [ "$CYPRESS_SORRY" == "true" ]; then
  echo "✅ [CYPRESS ENTRYPOINT] Usando Cypress Sorry (cypress-cloud)"

  CYPRESS_COMMAND="$(npm bin)/cypress-cloud run --env CYPRESS_SORRY=$CYPRESS_SORRY,TEST_ENVIRONMENT=$TEST_ENVIRONMENT,DISCOVERY_URL=$URL_DISCOVERY${CI_COMMIT_MESSAGE_ENV}${CYPRESS_BURN_PARAM} --browser chrome --record $CYPRESS_TAGS_PARAM --key $CYPRESS_RECORD_KEY --parallel --ci-build-id $CYPRESS_CI_BUILD_ID $CYPRESS_SPEC_PARAM $@"

  echo "🚀 [CYPRESS ENTRYPOINT] Comando completo que será executado:"
  echo "$CYPRESS_COMMAND"
  echo ""
  echo "🔗 [CYPRESS ENTRYPOINT] URLs importantes:"
  echo "   - Director URL (currents.config.js): $(grep -o 'http[^"]*' /e2e/currents.config.js || echo 'NÃO ENCONTRADA')"
  echo "   - URL_CYPRESS_SORRY_DIRECTOR: $URL_CYPRESS_SORRY_DIRECTOR"
  echo "   - URL_CYPRESS_SORRY_API: $URL_CYPRESS_SORRY_API"
  echo ""
  echo "⏰ [CYPRESS ENTRYPOINT] Iniciando execução do cypress-cloud..."

  $CYPRESS_COMMAND
else
  echo "✅ [CYPRESS ENTRYPOINT] Usando Cypress Cloud oficial"

  CYPRESS_COMMAND="cypress run --env CYPRESS_SORRY=$CYPRESS_SORRY,TEST_ENVIRONMENT=$TEST_ENVIRONMENT,DISCOVERY_URL=$URL_DISCOVERY${CI_COMMIT_MESSAGE_ENV}${CYPRESS_BURN_PARAM} --browser chrome --record $CYPRESS_TAGS_PARAM --key $CYPRESS_RECORD_KEY --parallel --ci-build-id $CYPRESS_CI_BUILD_ID $CYPRESS_SPEC_PARAM $@"

  echo "🚀 [CYPRESS ENTRYPOINT] Comando completo que será executado:"
  echo "$CYPRESS_COMMAND"
  echo ""
  echo "⏰ [CYPRESS ENTRYPOINT] Iniciando execução do cypress..."

  $CYPRESS_COMMAND
fi

