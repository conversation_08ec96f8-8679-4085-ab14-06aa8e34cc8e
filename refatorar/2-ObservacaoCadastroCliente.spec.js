// tests/operacoes.spec.js
describe('Operações', () => {

    // 🛠️ Funções Auxiliares
    function desmarcarCheckboxNovaVersao() {
        cy.get('#switch-nova-versao', { timeout: 10000 })
            .should('exist')
            .then($checkbox => {
                const isChecked = $checkbox.prop('checked');
                const hasCheckedClass = $checkbox.hasClass('checked');

                if (isChecked || hasCheckedClass) {
                    cy.get('.slider').should('be.visible').click({ force: true });
                    cy.log('Checkbox desmarcado com sucesso.');
                }
            });
    }

    function abrirObservacoes() {
        cy.get('#mdlObservacoesDiv').should('not.be.visible');
        cy.get('#form\\:verTodasObservacoes', { timeout: 10000 })
            .should('be.visible')
            .click({ force: true });
        cy.log('Modal de observações aberto.');
    }

    function loginEPrepararAmbiente(usuario) {
        cy.loginComApi(1, 'teste', usuario);
        cy.abrirFuncLupa('0030');
        desmarcarCheckboxNovaVersao();
    }

    // 🧪 Testes Gerais
    context('Operações Gerais', () => {
        beforeEach(() => {
            loginEPrepararAmbiente('pactobr');
            desmarcarCheckboxNovaVersao()
        });

        it('Adicionar uma nova observação', () => {
            cy.then(() => {
                return new Cypress.Promise(resolve => {
                  setTimeout(() => {
                    resolve(); // Quando o tempo expirar, o comando continuará
                  }, 1000); // Atraso de 1000ms (1 segundo)
                });
              });

            cy.get('#form\\:adicionarObservacaoText', { timeout: 10000 })
                .should('be.visible')
                .click();

            cy.get('#formMdlObservacoesAdicionar\\:textoObservacaoClienteAdicionar', { timeout: 30000 })
                .should('be.visible')
                .type('teste observacao');

            cy.get('#formMdlObservacoesAdicionar\\:confirmarObservacaoAdicionar', { timeout: 30000 })
                .should('be.visible')
                .click();

            cy.contains('teste observacao').should('be.visible');
            
        });

        it('Editar observação lançada', () => {
            cy.then(() => {
                return new Cypress.Promise(resolve => {
                  setTimeout(() => {
                    resolve(); // Quando o tempo expirar, o comando continuará
                  }, 1000); // Atraso de 1000ms (1 segundo)
                });
              });

            abrirObservacoes();

            cy.get('[id="formMdlObservacoes:listaObservacoes:0:alterarObservacaoMensagem"]', { timeout: 30000 })
                .should('be.visible')
                .click();

            cy.get('[id="formMdlObservacoes:listaObservacoes:0:gravarObservacaoMensagem"]', { timeout: 30000 })
                .should('be.visible')
                .click();

            cy.contains('teste observacao').should('be.visible');
        });

        it('Excluir observação', () => {
            abrirObservacoes();

            cy.get('[id="formMdlObservacoes:listaObservacoes:0:excluirObservacaoMensagem"]', { timeout: 30000 })
                .should('be.visible')
                .click();

            cy.get('[id="formMdlObservacoesConfirmarcao:confirmarExclussaoObservacao"]', { timeout: 30000 })
                .should('be.visible')
                .click();

        });
    });

    // 🧪 Testes para Consultor
    context('Permissões de Consultor', () => {
        beforeEach(() => {
            loginEPrepararAmbiente('Consultor2');
        });

        beforeEach(() => {
            desmarcarCheckboxNovaVersao();
        });

        it('Validar permissão de exclusão', () => {

            cy.then(() => {
                return new Cypress.Promise(resolve => {
                  setTimeout(() => {
                    resolve(); // Quando o tempo expirar, o comando continuará
                  }, 1000); // Atraso de 1000ms (1 segundo)
                });
              });
            abrirObservacoes();

            cy.get('[id="formMdlObservacoes:listaObservacoes:0:excluirObservacaoMensagem"]', { timeout: 30000 })
                .should('be.visible')
                .click();
        });

        it('Validar permissão de edição', () => {

            cy.then(() => {
                return new Cypress.Promise(resolve => {
                  setTimeout(() => {
                    resolve(); // Quando o tempo expirar, o comando continuará
                  }, 1000); // Atraso de 1000ms (1 segundo)
                });
              });

            abrirObservacoes();

            cy.get('[id="formMdlObservacoes:listaObservacoes:0:alterarObservacaoMensagem"]', { timeout: 30000 })
                .should('be.visible')
                .click();
        });
    });
});



