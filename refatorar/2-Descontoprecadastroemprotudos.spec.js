import DescontoPage from '../../../support/pages/Descontoprecadastroemprotudos';

const descontoPage = new DescontoPage();

describe('Adicionar desconto precadastrado', function () {

    beforeEach(() => {
        cy.loginComApi(1, "teste", "pactobr", "123");
    });

    it('Desconto precadastrado na matrícula', function () {
        descontoPage.visitMatriculaPage('000119');
        descontoPage.clickNovoContrato();
        cy.atualizarBV(0);
        descontoPage.selectPlanoDesconto();
        descontoPage.marcarComposicao();
        descontoPage.adicionarDesconto();
        descontoPage.senha();
        descontoPage.verificarTotalProdutos('0,00');
    });

    it('Desconto precadastrado na rematrícula', function () {
        descontoPage.visitarRematriculaPage('00025');
        descontoPage.clickRematricularContrato();
        cy.atualizarBV(0);
        descontoPage.selectPlanoDesconto();
        descontoPage.marcarComposicao();
        descontoPage.adicionarDesconto50();
        descontoPage.verificarTotalProdutos('0,00');
    });

});
