/// <reference types="cypress" />

const faker = require('faker-br');


const dataVencimento = Cypress.dayjs().format('DD/MM/YYYY');
const dataCompetencia = Cypress.dayjs().format('DD/MM/YYYY');

const descricaoContaReceber = faker.company.companyName();
const descricaoContaPagar = faker.company.companyName();

describe('Excluir parcela de contas a pagar e receber', function () {

    it('Cadastro e agendamento de conta a pagar', function () {
        cy.login('fin');
        cy.navigateToMenu('NOVA_CONTA_PAGAR');
        cy.fillPaymentForm(descricaoContaPagar, '10,00');
        cy.waitVisible('#formLanc\\:dataCompetenciaInputDate')
        cy.get('#formLanc\\:dataCompetenciaInputDate').clear({force:true}).type(dataCompetencia, {force: true})

        cy.waitVisible('#formLanc\\:dataVencimentoInputDate')
        cy.get('#formLanc\\:dataVencimentoInputDate').clear({force: true}).type(dataVencimento, {force: true})
        cy.get('#formLanc\\:agendarPagamento').click();
        cy.get('#agendamentoFinanceiroForm\\:gravarAgendamentoFin').should('be.visible').click();
        cy.get('#formLanc\\:panelAgendamentos_body').should('contain', `Parcelas`);
    });

    it('Excluir Parcela Agendamento de conta a pagar', function () {
        cy.login('fin');
        cy.navigateToMenu('CONTAS_A_PAGAR');
        cy.get('#form\\:btnRefazerConnsulta').click();
        cy.get('#formFiltrosLancamento\\:descricaoFiltro').clear().type(descricaoContaPagar);
        cy.get('#formFiltrosLancamento\\:consultarPesquisaPadrao').click();
        cy.wait(700);
        cy.get(`#form\\:items\\:0\\:editarPagamento`).should('be.visible').click();
        cy.wait(700);
        cy.intercept('**/faces/pages/finan/telaLancamentosForm.jsp').as('modalExcluir');
        cy.get('.botaoExcluir:first').click();
        cy.wait('@modalExcluir');
        cy.get('#formConfirmaExclusao\\:ModalExlusaoParcelaSim').should('be.visible');
        cy.get('#formConfirmaExclusao\\:ModalExlusaoParcelaSim').click();
        cy.get('#formLanc\\:msgContaPagar').should('contain', 'Dados Excluídos com Sucesso');
    });

    it('Cadastro e agendamento de conta a receber', function () {
        cy.login('fin');
        cy.navigateToMenu('NOVA_CONTAS_RECEBER');
        cy.fillPaymentForm(descricaoContaReceber, '10,00');
        cy.waitVisible('#formLanc\\:dataCompetenciaInputDate')
        cy.get('#formLanc\\:dataCompetenciaInputDate').clear({force:true}).type(dataCompetencia, {force: true})

        cy.waitVisible('#formLanc\\:dataVencimentoInputDate')
        cy.get('#formLanc\\:dataVencimentoInputDate').clear({force: true}).type(dataVencimento, {force: true})
        cy.get('#formLanc\\:agendarRecebimento').click();
        cy.wait(700);
        cy.get('#agendamentoFinanceiroForm\\:gravarAgendamentoFin').should('be.visible').click();
        cy.get('#formLanc\\:panelAgendamentos_body').should('contain', 'Parcelas');
    });

    it('Excluir conta a receber', () => {
        cy.login('fin');
        cy.navigateToMenu('CONTAS_A_RECEBER');
        cy.get('#form\\:btnRefazerConnsulta').click();
        cy.get('#formFiltrosLancamento\\:descricaoFiltro').clear().type(descricaoContaReceber);
        cy.get('#formFiltrosLancamento\\:consultarPesquisaPadrao').click();
        cy.wait(700);
        cy.get('#form\\:items\\:0\\:excluirPagamento').click();
        cy.waitVisible('#panelAutorizacaoFuncionalidadeCursorDiv')
        cy.get('#formSenhaAutorizacao\\:senha').click().type('123')
        cy.get('#formSenhaAutorizacao\\:btnAutorizar').click();
        cy.get('#form\\:confirmarExclusaoPagamento').click();
    });
});