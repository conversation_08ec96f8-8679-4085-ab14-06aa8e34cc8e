import CancelamentoPadraoAlterandoValor from '../../../support/pages/ADM/CancelamentoPadraoAlterandoValor';

describe("Cancelamento Padrão", () => {
    it("CANC.PADRAO-alterando valor da multa", () => {
      
      CancelamentoPadraoAlterandoValor.loginAdm();
  
      CancelamentoPadraoAlterandoValor.visitarPagina();
      CancelamentoPadraoAlterandoValor.alterarTipoCancelamento();
  
      
      CancelamentoPadraoAlterandoValor.logoutAdm();
  
      CancelamentoPadraoAlterandoValor.loginAdm();
      CancelamentoPadraoAlterandoValor.abrirAlunoLupa("000116");
      CancelamentoPadraoAlterandoValor.abrirContrato();
  
      CancelamentoPadraoAlterandoValor.realizarCancelamento();
      CancelamentoPadraoAlterandoValor.alterarValorMulta();
      CancelamentoPadraoAlterandoValor.autorizarAlteracao();
    });
  });