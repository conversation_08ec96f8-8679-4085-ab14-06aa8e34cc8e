describe('Observação', () => {
    beforeEach(() => {
      cy.loginComApi(1, 'teste', 'enzo');
   
      cy.get('[data-cy="modulo-sigla-NTR"]').should('be.visible').click()
      cy.get('#topbar-search-field', { timeout: 15000 }).type('<PERSON><PERSON>');
      cy.contains('<PERSON><PERSON>').click();
      cy.intercept('POST', '**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste').as('ultimaRequisicao');
      cy.wait('@ultimaRequisicao', { timeout: 50000 });
      
    });

    it("Acessos - Definir senha de acesso", function () {
       cy.get('button').contains('Mais opções').click();
       cy.get('#pch-opt-mais-acesso').contains('Acessos').click();
       cy.get('#pch-opt-acesso-definir-senha').contains('Definir senha de acesso').click();
       cy.get('.aux-wrapper > #inpt-mdsa-senha').click().type('12345')
       cy.get('.aux-wrapper > #inpt-mdsa-confirm-senha').click().type('12345')
       cy.get('#btn-mdsa-confirmar > .content').click()
       cy.contains('Senha alterada com sucesso !').should("be.visible")

      })
      it("Acessos - Bloqueio Mensagem na catraca", function () {
        cy.get('button').contains('Mais opções').click();
        cy.get('#pch-opt-mais-acesso').contains('Acessos').click();
        cy.get('#pch-opt-acesso-bloqueio').click();
        cy.get('#dt-data-bloqueio-input').click().type('03/02/2025')
        cy.get('pacto-cat-form-textarea > #inpt-txtar-mensagem').click().type('Teste Auto')
        cy.contains('Salvar aviso/bloqueio').click();
        cy.get('#aut-input-psw').click().type('123')
        cy.get('#aut-btn-confirm').click()
        cy.contains('Mensagem salva com sucesso!').should("be.visible")
  });

      it("Acessos - Registrar acesso manual", function () {
         cy.get('button').contains('Mais opções').click();
         cy.get('#pch-opt-mais-acesso').contains('Acessos').click();
         cy.get('#pch-opt-acesso-manual').click();
         cy.get('div.aux-parent select#slct-local-acesso', { timeout: 50000 })
           .should("be.visible")
           .select('1'); 
         cy.get('div.aux-parent select#slct-coletor')
           .should("be.visible")
           .select('1'); 
         cy.contains('Salvar').click()
         cy.get('.snotify.snotify-rightTop')
           .should('be.visible');
});
});