import {nome} from '../../../support/commands';
const dayjs = require('dayjs');

describe('Transferir alunos de Empresa', function () {

    beforeEach(() => {
        cy.logout()
        cy.login('adm')
    })

   
    it('Transferir aluno com parcelas vencidas', function(){  //é esperado que alunos com parcelas vencidas não possam ser transferidos

        const dataSubtraida = dayjs().subtract(5, 'day').format('DD/MM/YYYY');

        cy.CadastrarCliente()
        cy.waitVisible('#form\\:plano')
        cy.waitVisible('#form\\:btnAlterarDataLancamento')
        cy.get('#form\\:btnAlterarDataLancamento').click()
        cy.waitVisible('#formConfirmarAlteracao\\:dataBaseInputDate')
        cy.get('#formConfirmarAlteracao\\:dataBaseInputDate').clear().type(dataSubtraida)
        cy.waitVisible('#formConfirmarAlteracao\\:confirmar')
        cy.get('#formConfirmarAlteracao\\:confirmar').click()
        cy.waitVisible('#panelAutorizacaoFuncionalidadeCDiv')
        cy.get('#formSenhaAutorizacao\\:senha').click().type('123')
        cy.waitVisible('#formSenhaAutorizacao\\:confirmar')
        cy.get('#formSenhaAutorizacao\\:btnAutorizar').should('be.visible').click();
        cy.waitSelectValue('#form\\:plano', '12')
        cy.get('#form\\:plano').select('12')
        cy.waitVisible('#form\\:btnConferirNegociacao')
        cy.get('#form\\:btnConferirNegociacao').click()
        cy.waitVisible('#formConfirmarAlteracao2\\:confirmar')
        cy.get('#formConfirmarAlteracao2\\:confirmar').click()
        cy.waitVisible('#form\\:diasVencimento')
        cy.waitSelectValue('#form\\:diasVencimento', '0')
        cy.get('#form\\:diasVencimento').select('0')
        cy.waitVisible('#form\\:botaoConfirmarcao')
        cy.get('#form\\:botaoConfirmarcao').click()
        cy.waitVisible('#formSenhaAutorizacao\\:senha')
        cy.get('#formSenhaAutorizacao\\:senha').type('123')
        cy.waitVisible('#formSenhaAutorizacao\\:btnAutorizar')
        cy.get('#formSenhaAutorizacao\\:btnAutorizar').click()

         cy.log('passei por aqui')
         cy.logout()
         cy.login('adm', null, 'guizao');
         cy.visit('faces/preCadastro.jsp');
         cy.waitVisible('#form\\:termosConsulta');
         cy.get('body').then($el=>{
             cy.get('#form\\:termosConsulta').clear().type(nome);
             cy.waitVisible('#form\\:btnConsultarCliente');
             cy.get('#form\\:btnConsultarCliente').click();
             cy.waitVisible('#form\\:tableResults\\:tb');
             cy.get('#form\\:tableResults\\:tb').contains(nome.toUpperCase()).click();
             cy.waitVisible('#formExisteCliente\\:btnTransferirClienteEmpresa')
             cy.get('#formExisteCliente\\:btnTransferirClienteEmpresa').click()
             cy.waitVisible('#messageInfo')
             cy.get('#messageInfo').should('have.text','O cliente possui parcela(s) vencida(s) em aberto')
            
        })
    })



})



