{"name": "teste-auto", "version": "1.0.0", "engines": {"node": ">=18.0.0"}, "scripts": {"painel": "node painel.js", "open": "npm --prefix $(node get-zw-path.js) run open", "open:pipe": "npm --prefix $(node get-zw-path.js) run open:pipe", "open:local": "npm --prefix $(node get-zw-path.js) run open:local", "open:staging": "npm --prefix $(node get-zw-path.js) run open:staging", "open:zw74": "npm --prefix $(node get-zw-path.js) run open:zw74", "open:zw70": "npm --prefix $(node get-zw-path.js) run open:zw70", "test:local": "npm --prefix $(node get-zw-path.js) run test:local", "test:zw74": "npm --prefix $(node get-zw-path.js) run test:zw74", "test": "npm --prefix $(node get-zw-path.js) run test", "test:staging": "npm --prefix $(node get-zw-path.js) run test:staging", "pipec": "node scripts/connect-pipe.js", "piper": "node scripts/run.js", "build": "node scripts/build-cypress.js", "get-zw-path": "node get-zw-path.js"}, "dependencies": {"@vscode/sudo-prompt": "^9.3.1", "argparse": "^2.0.1", "axios": "^1.6.7", "dotenv": "^16.4.1", "express": "^4.21.1", "inquirer": "^6.0.0", "inquirer-autocomplete-prompt": "^1.0.2", "is-root": "^3.0.0", "js-yaml": "^4.1.0", "open": "^10.1.0", "ora": "^8.0.1", "teste-auto": "file:", "unzipper": "^0.10.14", "url": "^0.11.3", "wait-on": "^7.2.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.1", "@types/node": "^24.0.14", "cypress-grep": "^3.0.4", "typescript": "^5.8.3"}, "license": "UNLICENSED", "description": "Este projeto é uma aplicação de teste automatizado. Ele foi criado para facilitar a execução de testes em diferentes partes do nosso sistema.", "main": "get-zw-path.js", "keywords": [], "author": ""}