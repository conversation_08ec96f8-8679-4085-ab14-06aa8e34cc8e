.deploy_test_template: &deploy_test
  variables:
    CYPRESS_COMMIT_MESSAGE: ${CYPRESS_COMMIT_MESSAGE:-$CI_COMMIT_MESSAGE}
  script:
    - npm i 
    - node scripts/deploy-test-ci.js -c "$CYPRESS_COMMIT_MESSAGE" -sp $CYPRESS_SPEC -r $CYPRESS_REPLICAS -cs $CYPRESS_SORRY -t $APP_BRANCHS -bn $CYPRESS_BURN
  artifacts:
    paths:
      - zw/.env
      - zw/cypress.yml

deploy-test-staging:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "staging"
  tags:
    - deploy-teste-auto

deploy-test-oci-staging:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "oci-staging"
  tags:
    - deploy-teste-auto-oci-shell

deploy-test-prod:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "prod"
  tags:
    - swarm
    - locaweb
    - manager

deploy-test-oci-prod:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod"
  tags:
    - deploy-teste-auto-oci-shell

deploy-test-oci-prod-t3:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod-t3"
  tags:
    - deploy-teste-auto-oci-t3-shell

deploy-test-oci-staging-t3:
  <<: *deploy_test
  stage: test-scale
  rules:
    - if: $TEST_ENVIRONMENT == "oci-staging-t3"
  tags:
    - deploy-teste-auto-oci-t3-shell


.wait-test-complete_template: &wait_test_complete
  stage: test
  script:
    - npm i
    - node scripts/wait-test.js -s $CI_PIPELINE_ID

wait-test-complete-staging:
  <<: *wait_test_complete
  tags:
    - deploy-teste-auto
  rules:
    - if: $TEST_ENVIRONMENT == "staging"

wait-test-complete-oci-staging:
  <<: *wait_test_complete
  tags:
    - deploy-teste-auto-oci-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-staging"

wait-test-complete-prod:
  <<: *wait_test_complete
  tags:
    - swarm
    - locaweb
    - manager
  rules:
    - if: $TEST_ENVIRONMENT == "prod"

wait-test-complete-oci-prod:
  <<: *wait_test_complete
  tags:
    - deploy-teste-auto-oci-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod"

wait-test-complete-oci-prod-t3:
  <<: *wait_test_complete
  tags:
    - deploy-teste-auto-oci-t3-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod-t3"

wait-test-complete-oci-staging-t3:
  <<: *wait_test_complete
  tags:
    - deploy-teste-auto-oci-t3-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-staging-t3"

test-retry-prod:
  stage: test
  when: on_failure
  tags:
    - swarm
    - locaweb
    - manager
  rules:
    - if: $TEST_ENVIRONMENT == "prod" && $TEST_RETRY == "true"
      needs:
        - job: wait-test-complete-prod
        - job: deploy-test-prod
          artifacts: true
  script:
    - npm i
#    - node scripts/job-migrate.js
    - node scripts/test-retry.js -p $CI_PIPELINE_ID -e $TEST_ENVIRONMENT

test-retry-oci-prod:
  stage: test
  when: on_failure
  tags:
    - deploy-teste-auto-oci-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod" && $TEST_RETRY == "true"
      needs:
        - job: wait-test-complete-oci-prod
        - job: deploy-test-oci-prod
          artifacts: true
  script:
    - npm i
#    - node scripts/job-migrate.js
    - node scripts/test-retry.js -p $CI_PIPELINE_ID -e $TEST_ENVIRONMENT

test-retry-staging:
  stage: test
  when: on_failure
  tags:
    - deploy-teste-auto
  rules:
    - if: $TEST_ENVIRONMENT == "staging" && $TEST_RETRY == "true"
      needs:
        - job: wait-test-complete-staging
        - job: deploy-test-staging
          artifacts: true
  script:
    - npm i
    - node scripts/test-retry.js -p $CI_PIPELINE_ID -e $TEST_ENVIRONMENT -t $CI_JOB_TOKEN

test-retry-oci-staging:
  stage: test
  when: on_failure
  tags:
    - deploy-teste-auto-oci-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-staging" && $TEST_RETRY == "true"
      needs:
        - job: wait-test-complete-oci-staging
        - job: deploy-test-oci-staging
          artifacts: true
  script:
    - npm i
    - node scripts/test-retry.js -p $CI_PIPELINE_ID -e $TEST_ENVIRONMENT -t $CI_JOB_TOKEN

test-retry-oci-prod-t3:
  stage: test
  when: on_failure
  tags:
    - deploy-teste-auto-oci-t3-shell
  rules:
    - if: $TEST_ENVIRONMENT == "oci-prod-t3" && $TEST_RETRY == "true"
      needs:
        - job: wait-test-complete-oci-prod-t3
        - job: deploy-test-oci-prod-t3
          artifacts: true
  script:
    - echo "🔄 [TEST RETRY] Iniciando test-retry-oci-prod-t3..."
    - echo "📋 [TEST RETRY] Variáveis de ambiente:"
    - echo "   - TEST_ENVIRONMENT=$TEST_ENVIRONMENT"
    - echo "   - TEST_RETRY=$TEST_RETRY"
    - echo "   - CI_PIPELINE_ID=$CI_PIPELINE_ID"
    - echo "📁 [TEST RETRY] Verificando arquivos de artefatos..."
    - ls -la zw/ || echo "❌ Diretório zw/ não encontrado"
    - ls -la zw/.env || echo "❌ Arquivo zw/.env não encontrado"
    - npm i
    - echo "🚀 [TEST RETRY] Executando test-retry.js..."
    - node scripts/test-retry.js -p $CI_PIPELINE_ID -e $TEST_ENVIRONMENT

